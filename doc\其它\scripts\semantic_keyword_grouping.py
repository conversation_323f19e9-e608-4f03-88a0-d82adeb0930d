#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于大模型语义分析的关键词自动分组脚本
结合搜索量、相关性、竞争度三维度筛选
"""

import csv
import re
from collections import defaultdict
import json
import chardet

class SemanticKeywordGrouper:
    def __init__(self):
        # 基于语义分析的分组规则
        self.gender_groups = {
            'mens': ['mens', 'men', 'male', 'guys', 'gents', 'masculine', 'man'],
            'womens': ['women', 'ladies', 'female', 'girls', 'womans'],
            'neutral': []  # 默认分组
        }
        
        self.haircut_types = {
            'buzz_cut': ['buzz cut', 'buzzcut', 'buzz hair', 'military cut', 'army cut'],
            'classic_cuts': ['crew cut', 'caesar cut', 'pixie cut', 'bob cut', 'bowl cut'],
            'modern_styles': ['fade', 'taper', 'undercut', 'quiff', 'mullet', 'wolf cut'],
            'specialty': ['braids', 'cornrows', 'locs', 'dreadlocks', 'ponytail']
        }
        
        self.search_intents = {
            'informational': ['hair style', 'hairstyles', 'haircuts', 'hair cutting'],
            'local_service': ['near me', 'barber', 'salon', 'hair styling'],
            'tools_equipment': ['clippers', 'trimmer', 'scissors', 'wahl', 'razor'],
            'tutorials': ['how to', 'tutorial', 'guide', 'diy', 'self']
        }
        
        self.hair_length = {
            'very_short': ['buzz', 'crew', 'pixie', 'short short'],
            'short': ['short hair', 'short cut', 'short style'],
            'medium': ['medium', 'shoulder length'],
            'long': ['long hair', 'long style']
        }
        
        # 竞争度和CPC阈值
        self.cpc_tiers = {
            'high_value': 3.0,    # 高价值商业词
            'medium_value': 1.0,  # 中等价值
            'low_value': 0.5      # 信息型词汇
        }
    
    def detect_encoding(self, file_path):
        """检测文件编码"""
        with open(file_path, 'rb') as f:
            raw_data = f.read(10000)  # 读取前10KB检测编码
            result = chardet.detect(raw_data)
            return result['encoding']
        
    def categorize_keyword(self, keyword, search_vol, competition, cpc_low, cpc_high):
        """对单个关键词进行多维度分类"""
        keyword_lower = keyword.lower()
        
        # 搜索量分层
        if search_vol >= 5_000_000:
            volume_tier = 'tier1_5m'
        elif search_vol >= 500_000:
            volume_tier = 'tier2_500k'
        elif search_vol >= 50_000:
            volume_tier = 'tier3_50k'
        elif search_vol >= 5_000:
            volume_tier = 'tier4_5k'
        elif search_vol >= 500:
            volume_tier = 'tier5_500'
        else:
            volume_tier = 'tier6_100'
        
        # 性别分组
        gender = 'neutral'
        for group, keywords in self.gender_groups.items():
            if any(word in keyword_lower for word in keywords):
                gender = group
                break
        
        # 发型类型
        haircut_type = 'general'
        for group, keywords in self.haircut_types.items():
            if any(word in keyword_lower for word in keywords):
                haircut_type = group
                break
        
        # 搜索意图
        intent = 'informational'
        for group, keywords in self.search_intents.items():
            if any(word in keyword_lower for word in keywords):
                intent = group
                break
        
        # 头发长度
        length = 'unspecified'
        for group, keywords in self.hair_length.items():
            if any(word in keyword_lower for word in keywords):
                length = group
                break
        
        # 商业价值 (基于CPC)
        avg_cpc = (cpc_low + cpc_high) / 2 if cpc_low > 0 and cpc_high > 0 else 0
        if avg_cpc >= self.cpc_tiers['high_value']:
            commercial_value = 'high'
        elif avg_cpc >= self.cpc_tiers['medium_value']:
            commercial_value = 'medium'
        else:
            commercial_value = 'low'
        
        return {
            'keyword': keyword,
            'search_volume': search_vol,
            'volume_tier': volume_tier,
            'gender': gender,
            'haircut_type': haircut_type,
            'intent': intent,
            'length': length,
            'commercial_value': commercial_value,
            'competition': competition,
            'cpc_range': f"{cpc_low}-{cpc_high}"
        }
    
    def process_csv(self, csv_path):
        """处理整个CSV文件"""
        results = []
        stats = defaultdict(int)
        
        print("🔍 开始处理关键词数据...")
        
        # 自动检测编码
        encoding = self.detect_encoding(csv_path)
        print(f"📝 检测到文件编码: {encoding}")
        
        try:
            with open(csv_path, 'r', encoding=encoding) as f:
                reader = csv.reader(f)
                header = next(reader)  # 跳过表头
                print(f"📋 CSV表头: {header[:3]}...")  # 显示前3列
                
                for row_num, row in enumerate(reader, 2):
                    if len(row) < 7:
                        continue
                    
                    keyword = row[0].strip()
                    search_vol_str = row[1].strip()
                    
                    # 过滤无效搜索量
                    if not search_vol_str.isdigit():
                        continue
                    
                    search_vol = int(search_vol_str)
                    
                    # 只处理搜索量≥100的关键词
                    if search_vol < 100:
                        continue
                    
                    # 解析CPC数据
                    try:
                        cpc_low = float(row[6]) if row[6] and row[6] != '' else 0
                        cpc_high = float(row[7]) if row[7] and row[7] != '' else 0
                    except (ValueError, IndexError):
                        cpc_low = cpc_high = 0
                    
                    # 分类关键词
                    categorized = self.categorize_keyword(
                        keyword, search_vol, row[4] if len(row) > 4 else '', cpc_low, cpc_high
                    )
                    results.append(categorized)
                    
                    # 统计信息
                    stats['total'] += 1
                    stats[categorized['volume_tier']] += 1
                    stats[f"gender_{categorized['gender']}"] += 1
                    stats[f"intent_{categorized['intent']}"] += 1
                    
                    # 进度显示
                    if row_num % 1000 == 0:
                        print(f"  已处理 {row_num} 行...")
                        
        except UnicodeDecodeError as e:
            print(f"❌ 编码错误: {e}")
            print("尝试使用其他编码...")
            
            # 尝试常见编码
            encodings = ['gb2312', 'gbk', 'gb18030', 'cp1252', 'iso-8859-1']
            for enc in encodings:
                try:
                    print(f"尝试编码: {enc}")
                    with open(csv_path, 'r', encoding=enc) as f:
                        reader = csv.reader(f)
                        header = next(reader)
                        print(f"✅ 成功！使用编码: {enc}")
                        return self.process_csv_with_encoding(csv_path, enc)
                except:
                    continue
            
            raise Exception("无法找到合适的编码格式")
        
        return results, dict(stats)
    
    def process_csv_with_encoding(self, csv_path, encoding):
        """使用指定编码处理CSV"""
        results = []
        stats = defaultdict(int)
        
        with open(csv_path, 'r', encoding=encoding) as f:
            reader = csv.reader(f)
            header = next(reader)  # 跳过表头
            
            for row_num, row in enumerate(reader, 2):
                if len(row) < 2:
                    continue
                
                keyword = row[0].strip()
                search_vol_str = row[1].strip()
                
                if not search_vol_str.isdigit():
                    continue
                
                search_vol = int(search_vol_str)
                if search_vol < 100:
                    continue
                
                try:
                    cpc_low = float(row[6]) if len(row) > 6 and row[6] else 0
                    cpc_high = float(row[7]) if len(row) > 7 and row[7] else 0
                except:
                    cpc_low = cpc_high = 0
                
                categorized = self.categorize_keyword(
                    keyword, search_vol, row[4] if len(row) > 4 else '', cpc_low, cpc_high
                )
                results.append(categorized)
                
                stats['total'] += 1
                stats[categorized['volume_tier']] += 1
                stats[f"gender_{categorized['gender']}"] += 1
                stats[f"intent_{categorized['intent']}"] += 1
                
                if row_num % 1000 == 0:
                    print(f"  已处理 {row_num} 行...")
        
        return results, dict(stats)
    
    def generate_page_groups(self, categorized_keywords):
        """基于分类结果生成页面分组建议"""
        page_groups = defaultdict(list)
        
        for item in categorized_keywords:
            # 主页：Buzz Cut核心
            if 'buzz' in item['keyword'].lower():
                page_groups['homepage_buzz_cut'].append(item)
            
            # 男士发型页面
            elif item['gender'] == 'mens':
                if item['haircut_type'] == 'classic_cuts':
                    page_groups['mens_classic'].append(item)
                elif item['haircut_type'] == 'modern_styles':
                    page_groups['mens_modern'].append(item)
                else:
                    page_groups['mens_general'].append(item)
            
            # 女士发型页面  
            elif item['gender'] == 'womens':
                if item['length'] == 'very_short' or item['length'] == 'short':
                    page_groups['womens_short'].append(item)
                else:
                    page_groups['womens_general'].append(item)
            
            # 本地服务页面
            elif item['intent'] == 'local_service':
                page_groups['local_services'].append(item)
            
            # 工具设备页面
            elif item['intent'] == 'tools_equipment':
                page_groups['tools_guides'].append(item)
            
            # 教程页面
            elif item['intent'] == 'tutorials':
                page_groups['tutorials'].append(item)
            
            # 其他通用页面
            else:
                page_groups['general_hairstyles'].append(item)
        
        return dict(page_groups)
    
    def export_results(self, page_groups, stats, output_path='keyword_analysis_results.json'):
        """导出分析结果"""
        page_stats = {}
        for page, keywords in page_groups.items():
            total_volume = sum(k['search_volume'] for k in keywords)
            
            # 安全计算平均CPC
            valid_cpc = []
            for k in keywords:
                try:
                    cpc_parts = k['cpc_range'].split('-')
                    if len(cpc_parts) == 2 and cpc_parts[0] != '0':
                        valid_cpc.append(float(cpc_parts[0]))
                except:
                    continue
            
            avg_cpc = sum(valid_cpc) / len(valid_cpc) if valid_cpc else 0
            
            page_stats[page] = {
                'keyword_count': len(keywords),
                'total_monthly_volume': total_volume,
                'avg_cpc': round(avg_cpc, 2),
                'top_keywords': sorted(keywords, key=lambda x: x['search_volume'], reverse=True)[:10]
            }
        
        result = {
            'summary_stats': stats,
            'page_groups': page_stats,
            'recommendations': self.generate_recommendations(page_stats)
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"📊 分析结果已导出到: {output_path}")
        return result
    
    def generate_recommendations(self, page_stats):
        """生成SEO和商业化建议"""
        recommendations = []
        
        # 找出高价值页面
        high_value_pages = []
        for page, stats in page_stats.items():
            if stats['total_monthly_volume'] > 1_000_000:
                high_value_pages.append((page, stats))
        
        recommendations.append({
            "type": "high_priority_pages",
            "pages": [p[0] for p in high_value_pages],
            "reason": "月搜索量超过100万，优先开发"
        })
        
        # 商业化机会
        commercial_pages = [
            page for page, stats in page_stats.items() 
            if stats['avg_cpc'] > 2.0
        ]
        
        recommendations.append({
            "type": "monetization_opportunities", 
            "pages": commercial_pages,
            "reason": "平均CPC>$2，适合联盟营销或付费服务"
        })
        
        return recommendations

def main():
    grouper = SemanticKeywordGrouper()
    
    try:
        # 处理CSV文件
        categorized_keywords, stats = grouper.process_csv('doc/谷歌关键词规划+综合.csv')
        
        # 生成页面分组
        page_groups = grouper.generate_page_groups(categorized_keywords)
        
        # 导出结果
        results = grouper.export_results(page_groups, stats)
        
        # 打印摘要
        print(f"\n📈 分析摘要:")
        print(f"总关键词数 (搜索量≥100): {stats['total']:,}")
        print(f"页面分组数: {len(page_groups)}")
        
        print(f"\n🎯 推荐页面优先级:")
        for rec in results['recommendations']:
            print(f"- {rec['type']}: {rec['pages']}")
            print(f"  原因: {rec['reason']}")
            
        # 显示各页面统计
        print(f"\n📄 页面分组详情:")
        for page, group in page_groups.items():
            total_vol = sum(k['search_volume'] for k in group)
            print(f"- {page}: {len(group)} 个关键词, 总流量: {total_vol:,}/月")
            
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        print("请检查CSV文件路径和格式")

if __name__ == "__main__":
    main() 