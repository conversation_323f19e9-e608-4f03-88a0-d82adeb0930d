import { Header } from "@/types/blocks/header";
import { Footer } from "@/types/blocks/footer";

// 复用相同的PageMetadata类型
export interface PageMetadata {
  title?: string;
  description?: string;
  keywords?: string;
}

// 脸型数据类型
export interface FaceShape {
  id: string;
  name: string;
  image: string;
  description: string;
  recommendedStyles: string[];
}

// 发型样式类型
export interface HairstyleItem {
  id: string;
  name: string;
  image: string;
  category: string;
}

// 工具项目类型
export interface ToolItem {
  id: string;
  name: string;
  description: string;
  image: string;
  price: string;
  rating: number;
  url: string;
}

// 美发师信息类型
export interface StylistInfo {
  id: string;
  name: string;
  image: string;
  specialty: string[];
  rating: number;
  location: string;
  experience: string;
}

// Demo页面结构
export interface DemoPage {
  metadata?: PageMetadata;
  header?: Header;
  hero: {
    title: string;
    subtitle: string;
    primaryCta: string;
    secondaryCta: string;
  };
  intro: {
    title: string;
    description: string;
    benefits: string[];
    image: string;
  };
  styles: {
    title: string;
    description: string;
    categories: Array<{
      id: string;
      name: string;
      styles: HairstyleItem[];
    }>;
  };
  faceShape: {
    title: string;
    subtitle: string;
    description: string;
    shapes: FaceShape[];
  };
  aiTryOn: {
    title: string;
    subtitle: string;
    description: string;
    demoImage: string;
    resultImage: string;
    hairstyles: HairstyleItem[];
  };
  tools: {
    title: string;
    description: string;
    items: ToolItem[];
  };
  stylists?: {
    title: string;
    description: string;
    featured: StylistInfo[];
  };
  footer?: Footer;
} 