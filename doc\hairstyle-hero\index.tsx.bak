import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Play, ArrowRight, Star } from "lucide-react";

interface HairstyleHeroProps {
  hero: {
    title: string;
    subtitle?: string;
    description: string;
    mainImage: string;
    beforeAfterImages?: {
      before: string;
      after: string;
    };
    stats?: Array<{
      number: string;
      label: string;
    }>;
    features?: string[];
    ctaText: string;
    ctaSecondary?: string;
    badges?: string[];
    rating?: {
      score: number;
      reviews: number;
    };
  };
}

export default function HairstyleHero({ hero }: HairstyleHeroProps) {
  return (
    <section className="relative py-20 px-4 bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]" />
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            {/* Badges */}
            {hero.badges && hero.badges.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {hero.badges.map((badge, index) => (
                  <Badge key={index} className="bg-white/10 text-white border-white/20">
                    {badge}
                  </Badge>
                ))}
              </div>
            )}

            {/* Title */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                {hero.title}
              </h1>
              {hero.subtitle && (
                <p className="text-xl md:text-2xl text-gray-300">
                  {hero.subtitle}
                </p>
              )}
            </div>

            {/* Description */}
            <p className="text-lg text-gray-300 max-w-xl">
              {hero.description}
            </p>

            {/* Features */}
            {hero.features && hero.features.length > 0 && (
              <div className="space-y-2">
                {hero.features.slice(0, 4).map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full" />
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>
            )}

            {/* Rating */}
            {hero.rating && (
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={`w-5 h-5 ${
                        i < Math.floor(hero.rating!.score)
                          ? "text-yellow-400 fill-current"
                          : "text-gray-400"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-gray-300">
                  {hero.rating.score}/5 ({hero.rating.reviews} reviews)
                </span>
              </div>
            )}

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-white text-black hover:bg-gray-100 transition-colors">
                <ArrowRight className="w-5 h-5 mr-2" />
                {hero.ctaText}
              </Button>
              {hero.ctaSecondary && (
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                  <Play className="w-5 h-5 mr-2" />
                  {hero.ctaSecondary}
                </Button>
              )}
            </div>

            {/* Stats */}
            {hero.stats && hero.stats.length > 0 && (
              <div className="grid grid-cols-3 gap-6 pt-8 border-t border-white/20">
                {hero.stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-2xl md:text-3xl font-bold text-white">
                      {stat.number}
                    </div>
                    <div className="text-sm text-gray-400">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Right Content - Images */}
          <div className="relative">
            {hero.beforeAfterImages ? (
              /* Before/After Comparison */
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <p className="text-sm text-gray-400 text-center">Before</p>
                  <div className="relative h-80 rounded-lg overflow-hidden">
                    <Image
                      src={hero.beforeAfterImages.before}
                      alt="Before hairstyle"
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-gray-400 text-center">After</p>
                  <div className="relative h-80 rounded-lg overflow-hidden">
                    <Image
                      src={hero.beforeAfterImages.after}
                      alt="After hairstyle"
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
              </div>
            ) : (
              /* Single Main Image */
              <div className="relative h-96 lg:h-[500px] rounded-xl overflow-hidden">
                <Image
                  src={hero.mainImage}
                  alt={hero.title}
                  fill
                  className="object-cover"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
} 