'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface StyleItem {
  id: string;
  title: string;
  description: string;
  image: string;
  url: string;
  tags: string[];
}

interface PopularHairStylesProps {
  styles: StyleItem[];
}

export default function PopularHairStyles({ styles }: PopularHairStylesProps) {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  
  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {styles.map((style) => (
          <div 
            key={style.id}
            onMouseEnter={() => setHoveredItem(style.id)}
            onMouseLeave={() => setHoveredItem(null)}
            className="h-full"
          >
            <Card className="h-full overflow-hidden hover:shadow-lg transition-shadow duration-300 border border-slate-200">
              <div className="relative aspect-[3/2] overflow-hidden bg-slate-100">
                <img
                  src={style.image}
                  alt={`${style.title} - popular haircuts and gents hair style`}
                  className={`w-full h-full object-contain transition-all duration-500 ${
                    hoveredItem === style.id ? 'scale-105' : 'scale-100'
                  }`}
                  style={{
                    objectPosition: 'center top',
                  }}
                  onError={(e) => {
                    e.currentTarget.style.objectFit = 'cover';
                    e.currentTarget.style.objectPosition = 'center';
                  }}
                />
                
                <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/80 via-black/40 to-transparent">
                  <h3 className="text-lg font-bold text-white drop-shadow-lg">{style.title}</h3>
                </div>
                
                <div className={`absolute top-3 right-3 transition-opacity duration-300 ${
                  hoveredItem === style.id ? 'opacity-100' : 'opacity-0'
                }`}>
                  <div className="bg-white/90 backdrop-blur-sm rounded-full p-2">
                    <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </div>
                </div>
              </div>
              
              <CardContent className="p-4">
                <p className="text-sm text-slate-600 mb-3 line-clamp-2">
                  {style.description}
                </p>
                
                <div className="flex flex-wrap gap-1.5 mb-3">
                  {style.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs bg-slate-100 text-slate-700 hover:bg-blue-100 hover:text-blue-700 transition-colors">
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                <Link href={style.url}>
                  <Button 
                    className={`w-full text-sm transition-all duration-300 ${
                      hoveredItem === style.id 
                        ? 'bg-blue-700 hover:bg-blue-800 shadow-lg transform translate-y-[-1px]' 
                        : 'bg-blue-600 hover:bg-blue-700'
                    } text-white`}
                    aria-label={`Learn more about ${style.title} hair style and haircuts`}
                  >
                    <span className="flex items-center justify-center gap-2">
                    View Style
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        ))}
      </div>
      
      <div className="text-center mt-8">
        <p className="text-sm text-slate-500 mb-4">
          Discover more buzz cut variations and gents hair styles in our complete collection
        </p>
        <Link href="/men">
          <Button variant="outline" className="border-blue-500 text-blue-600 hover:bg-blue-50 hover:border-blue-600 transition-all duration-300">
            <span className="flex items-center gap-2">
            Browse All Haircuts
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </span>
          </Button>
        </Link>
      </div>
    </div>
  );
}