"""
通用关键词分类算法框架
Author: AI Assistant
Version: 1.0
Description: 可复用的关键词分类和页面分配算法
"""

import pandas as pd
import numpy as np
import re
from collections import defaultdict
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod
import json


@dataclass
class KeywordData:
    """关键词数据结构"""
    keyword: str
    search_volume: int
    competition: float
    additional_data: Dict[str, Any] = None


@dataclass
class ClassificationResult:
    """分类结果数据结构"""
    keyword: str
    search_volume: int
    competition: float
    target_page: str
    page_description: str
    match_score: float
    primary_category: str
    secondary_categories: List[str] = None
    confidence: float = 0.0


class ClassificationRule:
    """单个分类规则"""
    
    def __init__(self, 
                 page_url: str,
                 description: str,
                 exact_keywords: List[str] = None,
                 regex_patterns: List[str] = None,
                 volume_threshold: int = 0,
                 max_keywords: int = 100,
                 priority: int = 1):
        """
        初始化分类规则
        
        Args:
            page_url: 目标页面URL
            description: 页面描述
            exact_keywords: 精确匹配的关键词列表
            regex_patterns: 正则表达式模式列表
            volume_threshold: 最低搜索量阈值
            max_keywords: 最大关键词数量
            priority: 规则优先级（数字越小优先级越高）
        """
        self.page_url = page_url
        self.description = description
        self.exact_keywords = exact_keywords or []
        self.regex_patterns = regex_patterns or []
        self.volume_threshold = volume_threshold
        self.max_keywords = max_keywords
        self.priority = priority
        self.assigned_count = 0
    
    def calculate_match_score(self, keyword: str, search_volume: int) -> float:
        """
        计算关键词匹配分数
        
        Args:
            keyword: 关键词
            search_volume: 搜索量
            
        Returns:
            匹配分数（0-100）
        """
        if search_volume < self.volume_threshold:
            return 0.0
        
        if self.assigned_count >= self.max_keywords:
            return 0.0
        
        keyword_lower = keyword.lower().strip()
        score = 0.0
        
        # 精确匹配得分
        for exact_keyword in self.exact_keywords:
            if keyword_lower == exact_keyword.lower():
                score += 100.0  # 精确匹配最高分
                break
            elif exact_keyword.lower() in keyword_lower:
                score += 50.0   # 包含匹配
        
        # 正则模式匹配得分
        for pattern in self.regex_patterns:
            try:
                if re.search(pattern, keyword_lower, re.IGNORECASE):
                    score += 30.0
                    break  # 只计算第一个匹配的模式
            except re.error:
                continue
        
        # 优先级调整
        score = score / self.priority if self.priority > 0 else score
        
        return min(score, 100.0)


class KeywordClassificationFramework:
    """通用关键词分类框架"""
    
    def __init__(self):
        """初始化分类框架"""
        self.classification_rules: List[ClassificationRule] = []
        self.fallback_rule: Optional[ClassificationRule] = None
        self.processed_count = 0
        self.skipped_count = 0
        self.error_count = 0
    
    def add_rule(self, rule: ClassificationRule):
        """添加分类规则"""
        self.classification_rules.append(rule)
        # 按优先级排序
        self.classification_rules.sort(key=lambda r: r.priority)
    
    def set_fallback_rule(self, rule: ClassificationRule):
        """设置兜底规则"""
        self.fallback_rule = rule
    
    def load_rules_from_config(self, config_path: str):
        """从配置文件加载规则"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            for rule_config in config.get('rules', []):
                rule = ClassificationRule(
                    page_url=rule_config['page_url'],
                    description=rule_config['description'],
                    exact_keywords=rule_config.get('exact_keywords', []),
                    regex_patterns=rule_config.get('regex_patterns', []),
                    volume_threshold=rule_config.get('volume_threshold', 0),
                    max_keywords=rule_config.get('max_keywords', 100),
                    priority=rule_config.get('priority', 1)
                )
                self.add_rule(rule)
            
            # 加载兜底规则
            if 'fallback_rule' in config:
                fb_config = config['fallback_rule']
                fallback = ClassificationRule(
                    page_url=fb_config['page_url'],
                    description=fb_config['description'],
                    exact_keywords=fb_config.get('exact_keywords', []),
                    regex_patterns=fb_config.get('regex_patterns', [r'.*']),
                    volume_threshold=fb_config.get('volume_threshold', 1),
                    max_keywords=fb_config.get('max_keywords', 10000),
                    priority=fb_config.get('priority', 999)
                )
                self.set_fallback_rule(fallback)
                
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    def classify_keyword(self, keyword_data: KeywordData) -> ClassificationResult:
        """
        对单个关键词进行分类
        
        Args:
            keyword_data: 关键词数据
            
        Returns:
            分类结果
        """
        best_rule = None
        best_score = 0.0
        
        # 遍历所有规则找到最佳匹配
        for rule in self.classification_rules:
            score = rule.calculate_match_score(
                keyword_data.keyword, 
                keyword_data.search_volume
            )
            
            if score > best_score:
                best_score = score
                best_rule = rule
        
        # 如果没有匹配的规则，使用兜底规则
        if best_rule is None and self.fallback_rule:
            best_rule = self.fallback_rule
            best_score = 1.0
        
        # 创建分类结果
        if best_rule:
            best_rule.assigned_count += 1
            
            return ClassificationResult(
                keyword=keyword_data.keyword,
                search_volume=keyword_data.search_volume,
                competition=keyword_data.competition,
                target_page=best_rule.page_url,
                page_description=best_rule.description,
                match_score=best_score,
                primary_category=self._extract_primary_category(best_rule.page_url),
                confidence=min(best_score / 100.0, 1.0)
            )
        else:
            # 没有任何规则匹配
            return ClassificationResult(
                keyword=keyword_data.keyword,
                search_volume=keyword_data.search_volume,
                competition=keyword_data.competition,
                target_page="/unclassified/",
                page_description="未分类",
                match_score=0.0,
                primary_category="unclassified",
                confidence=0.0
            )
    
    def classify_batch(self, keywords_data: List[KeywordData]) -> List[ClassificationResult]:
        """
        批量分类关键词
        
        Args:
            keywords_data: 关键词数据列表
            
        Returns:
            分类结果列表
        """
        results = []
        
        for keyword_data in keywords_data:
            try:
                result = self.classify_keyword(keyword_data)
                results.append(result)
                self.processed_count += 1
                
                if self.processed_count % 1000 == 0:
                    print(f"已处理 {self.processed_count} 个关键词...")
                    
            except Exception as e:
                self.error_count += 1
                print(f"处理关键词 '{keyword_data.keyword}' 时出错: {e}")
                continue
        
        return results
    
    def _extract_primary_category(self, page_url: str) -> str:
        """从页面URL提取主分类"""
        parts = page_url.strip('/').split('/')
        if len(parts) >= 2:
            return parts[1]
        elif len(parts) == 1:
            return parts[0]
        else:
            return "other"
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取分类统计信息"""
        rule_stats = []
        total_assigned = 0
        
        for rule in self.classification_rules:
            rule_stats.append({
                'page_url': rule.page_url,
                'description': rule.description,
                'assigned_count': rule.assigned_count,
                'max_keywords': rule.max_keywords,
                'utilization': rule.assigned_count / rule.max_keywords if rule.max_keywords > 0 else 0
            })
            total_assigned += rule.assigned_count
        
        if self.fallback_rule:
            rule_stats.append({
                'page_url': self.fallback_rule.page_url,
                'description': self.fallback_rule.description,
                'assigned_count': self.fallback_rule.assigned_count,
                'max_keywords': self.fallback_rule.max_keywords,
                'utilization': self.fallback_rule.assigned_count / self.fallback_rule.max_keywords if self.fallback_rule.max_keywords > 0 else 0
            })
            total_assigned += self.fallback_rule.assigned_count
        
        return {
            'total_processed': self.processed_count,
            'total_assigned': total_assigned,
            'total_skipped': self.skipped_count,
            'total_errors': self.error_count,
            'coverage_rate': total_assigned / self.processed_count if self.processed_count > 0 else 0,
            'rule_statistics': rule_stats
        }
    
    def reset_counters(self):
        """重置计数器"""
        self.processed_count = 0
        self.skipped_count = 0
        self.error_count = 0
        
        for rule in self.classification_rules:
            rule.assigned_count = 0
        
        if self.fallback_rule:
            self.fallback_rule.assigned_count = 0


class DataProcessor:
    """数据处理工具类"""
    
    @staticmethod
    def safe_convert_to_int(value, default=0):
        """安全转换为整数"""
        try:
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return default
            return int(float(value))
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def safe_convert_to_float(value, default=0.0):
        """安全转换为浮点数"""
        try:
            if pd.isna(value) or value == '' or str(value).lower() == 'nan':
                return default
            return float(value)
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def clean_keyword(keyword):
        """清理关键词"""
        if pd.isna(keyword):
            return None
        
        keyword_str = str(keyword).strip()
        
        if (keyword_str == '' or 
            keyword_str.lower() == 'nan' or 
            keyword_str.lower() == 'none' or 
            len(keyword_str) < 2):
            return None
            
        return keyword_str
    
    @staticmethod
    def load_keywords_from_csv(file_path: str, 
                              keyword_column: str = 'Keyword',
                              volume_column: str = 'Avg. monthly searches',
                              competition_column: str = 'Competition (indexed value)') -> List[KeywordData]:
        """从CSV文件加载关键词数据"""
        keywords_data = []
        
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    print(f"使用 {encoding} 编码成功读取 {len(df)} 行数据")
                    break
                except:
                    continue
            
            if df is None:
                raise Exception("无法读取文件")
            
            # 删除完全空白的行
            df = df.dropna(how='all')
            
            for index, row in df.iterrows():
                # 清理关键词
                keyword = DataProcessor.clean_keyword(row[keyword_column])
                if keyword is None:
                    continue
                
                # 安全转换数值
                search_volume = DataProcessor.safe_convert_to_int(row[volume_column], 0)
                competition = DataProcessor.safe_convert_to_float(row[competition_column], 0.0)
                
                keywords_data.append(KeywordData(
                    keyword=keyword,
                    search_volume=search_volume,
                    competition=competition
                ))
            
            print(f"成功加载 {len(keywords_data)} 个有效关键词")
            return keywords_data
            
        except Exception as e:
            print(f"加载CSV文件失败: {e}")
            return []


class ResultExporter:
    """结果导出工具类"""
    
    @staticmethod
    def format_search_volume(volume: int) -> str:
        """格式化搜索量显示"""
        if volume >= 1000000:
            return f"{volume//1000000}M"
        elif volume >= 1000:
            return f"{volume//1000}K"
        else:
            return str(volume)
    
    @staticmethod
    def export_to_csv(results: List[ClassificationResult], 
                     output_file: str,
                     format_style: str = "table"):
        """导出结果到CSV文件"""
        
        if format_style == "table":
            ResultExporter._export_table_format(results, output_file)
        elif format_style == "flat":
            ResultExporter._export_flat_format(results, output_file)
        else:
            raise ValueError(f"不支持的格式: {format_style}")
    
    @staticmethod
    def _export_table_format(results: List[ClassificationResult], output_file: str):
        """导出为表格格式（类似截图样式）"""
        # 按页面分组
        page_groups = defaultdict(list)
        for result in results:
            page_groups[result.target_page].append(result)
        
        # 按页面重要性排序
        page_priority = {
            '/': 1,
            '/men/': 2,
            '/women/': 3,
            '/other/': 999
        }
        
        sorted_pages = sorted(page_groups.items(), 
                             key=lambda x: page_priority.get(x[0], 500))
        
        csv_data = []
        
        for page_url, page_results in sorted_pages:
            if not page_results:
                continue
            
            # 按搜索量排序
            page_results.sort(key=lambda x: x.search_volume, reverse=True)
            
            page_description = page_results[0].page_description
            total_volume = sum(r.search_volume for r in page_results)
            
            # 添加页面标题行
            csv_data.append({
                '主关键词': f"=== {page_description} ({page_url}) ===",
                '搜索量': f"共{len(page_results)}个词",
                'KD值': f"总量{ResultExporter.format_search_volume(total_volume)}",
                '页面': '',
                '话题': '',
                '大纲': ''
            })
            
            # 添加关键词数据
            for i, result in enumerate(page_results):
                if i == 0:
                    # 主关键词
                    csv_data.append({
                        '主关键词': result.keyword,
                        '搜索量': ResultExporter.format_search_volume(result.search_volume),
                        'KD值': int(result.competition),
                        '页面': page_url,
                        '话题': page_description,
                        '大纲': ''
                    })
                else:
                    # 次级关键词
                    csv_data.append({
                        '主关键词': '',
                        '搜索量': ResultExporter.format_search_volume(result.search_volume),
                        'KD值': int(result.competition),
                        '页面': '',
                        '话题': result.keyword,
                        '大纲': ''
                    })
            
            # 添加空行分隔
            csv_data.append({
                '主关键词': '',
                '搜索量': '',
                'KD值': '',
                '页面': '',
                '话题': '',
                '大纲': ''
            })
        
        # 保存到CSV
        result_df = pd.DataFrame(csv_data)
        result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"表格格式结果已保存到: {output_file}")
    
    @staticmethod
    def _export_flat_format(results: List[ClassificationResult], output_file: str):
        """导出为平铺格式"""
        csv_data = []
        
        for result in results:
            csv_data.append({
                'keyword': result.keyword,
                'search_volume': result.search_volume,
                'competition': result.competition,
                'target_page': result.target_page,
                'page_description': result.page_description,
                'match_score': result.match_score,
                'primary_category': result.primary_category,
                'confidence': result.confidence
            })
        
        result_df = pd.DataFrame(csv_data)
        result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"平铺格式结果已保存到: {output_file}")
    
    @staticmethod
    def generate_summary_report(results: List[ClassificationResult], 
                               statistics: Dict[str, Any],
                               output_file: str):
        """生成汇总报告"""
        report = []
        report.append("📊 关键词分类汇总报告")
        report.append("=" * 60)
        
        report.append(f"\n📈 总体统计:")
        report.append(f"  总关键词数: {statistics['total_processed']}")
        report.append(f"  成功分类: {statistics['total_assigned']}")
        report.append(f"  覆盖率: {statistics['coverage_rate']:.1%}")
        report.append(f"  跳过数量: {statistics['total_skipped']}")
        report.append(f"  错误数量: {statistics['total_errors']}")
        
        # 页面分配统计
        report.append(f"\n🏆 页面分配详情:")
        for rule_stat in statistics['rule_statistics']:
            if rule_stat['assigned_count'] > 0:
                report.append(f"  📄 {rule_stat['page_description']}")
                report.append(f"     URL: {rule_stat['page_url']}")
                report.append(f"     分配词数: {rule_stat['assigned_count']}")
                report.append(f"     利用率: {rule_stat['utilization']:.1%}")
                report.append("")
        
        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print(f"汇总报告已保存到: {output_file}")


# 使用示例
def example_usage():
    """使用示例"""
    
    # 1. 创建分类框架
    classifier = KeywordClassificationFramework()
    
    # 2. 添加分类规则
    buzz_cut_rule = ClassificationRule(
        page_url="/men/ultra-short/buzz-cut/",
        description="Buzz Cut 主页面",
        exact_keywords=["buzz cut", "buzzcut", "buzz cut for men"],
        regex_patterns=[r'\bbuzz\s*cut\b(?!.*fade)', r'\bbuzzcut\b'],
        volume_threshold=10,
        max_keywords=50,
        priority=1
    )
    classifier.add_rule(buzz_cut_rule)
    
    # 3. 设置兜底规则
    fallback_rule = ClassificationRule(
        page_url="/other/",
        description="其他页面",
        regex_patterns=[r'.*'],
        volume_threshold=1,
        max_keywords=10000,
        priority=999
    )
    classifier.set_fallback_rule(fallback_rule)
    
    # 4. 加载数据
    keywords_data = DataProcessor.load_keywords_from_csv("keywords.csv")
    
    # 5. 执行分类
    results = classifier.classify_batch(keywords_data)
    
    # 6. 导出结果
    ResultExporter.export_to_csv(results, "classification_results.csv", "table")
    
    # 7. 生成统计报告
    stats = classifier.get_statistics()
    ResultExporter.generate_summary_report(results, stats, "classification_summary.txt")
    
    print("分类完成!")


if __name__ == "__main__":
    example_usage() 