'use client';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { useState } from 'react';

interface Hairstyle {
  id: string;
  name: string;
  image: string;
  category: string;
}

interface AITryOnProps {
  section: {
    title: string;
    subtitle?: string;
    description?: string;
    demoImage: string;
    resultImage: string;
    hairstyles: Hairstyle[];
    uploadText?: string;
    tryOnText?: string;
    resetText?: string;
  };
}

export default function AITryOn({ section }: AITryOnProps) {
  const [selectedStyle, setSelectedStyle] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  const handleImageError = (imagePath: string) => {
    console.error('Failed to load demo image');
    setImageErrors(prev => new Set([...prev, imagePath]));
  };
  
  // 检查是否有有效的发型数据
  if (!section.hairstyles || !Array.isArray(section.hairstyles) || section.hairstyles.length === 0) {
    return (
      <section className="py-16 md:py-20 bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center max-w-4xl mx-auto mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-slate-900">
                {section.title || "AI Virtual Try-On"}
        </h2>
              <p className="text-lg text-slate-600 leading-relaxed">
                Hairstyle collection is coming soon. Stay tuned for exciting updates!
              </p>
            </div>
          </div>
      </div>
      </section>
    );
  }

  // 获取有效的分类
  const categories = ['All', ...Array.from(
    new Set(
      section.hairstyles
        .filter(style => style && style.category)
        .map(style => style.category)
    )
  )];

  // 过滤发型
  const filteredHairstyles = selectedCategory === 'All' 
    ? section.hairstyles 
    : section.hairstyles.filter(style => style.category === selectedCategory);
  
  return (
    <section className="py-16 md:py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* 标题区域 - 增强颜色对比度 */}
          <div className="text-center max-w-4xl mx-auto mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-slate-900">
              {section.title || "AI Virtual Try-On"}
        </h2>
        
        {section.subtitle && (
              <p className="text-xl md:text-2xl text-blue-700 mb-6 font-semibold">
            {section.subtitle}
          </p>
        )}
        
        {section.description && (
              <p className="text-lg text-slate-700 leading-relaxed font-medium">
            {section.description}
          </p>
        )}
      </div>
      
          {/* 主要内容区域 */}
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12">
            {/* 左侧：照片上传和预览区域 */}
            <div className="space-y-6">
              {/* 上传区域 */}
              <div className="bg-gradient-to-br from-white to-slate-50 p-8 rounded-2xl border border-slate-200 shadow-lg">
                <h3 className="text-2xl font-semibold text-slate-900 mb-6 flex items-center gap-3">
                  <span className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-sm font-bold text-white">1</span>
                  {section.uploadText || "Upload Your Photo"}
                </h3>
                
                <div className="relative h-80 bg-gradient-to-br from-slate-100 to-slate-200 rounded-xl overflow-hidden border-2 border-dashed border-slate-300 hover:border-blue-500 transition-colors">
                  {section.demoImage && !imageErrors.has(section.demoImage) ? (
                    <Image
                      src={section.demoImage}
                      alt="Demo photo"
                      fill
                      className="object-cover"
                      onError={() => handleImageError(section.demoImage)}
                  />
                ) : (
                    // 使用可用的图片作为fallback
                    <Image
                      src="/imgs/showcases/buzz-cut-transformation.jpg"
                      alt="Demo photo" 
                      fill
                      className="object-cover"
                    />
                  )}
                  <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/40 hover:bg-black/30 transition-colors">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <Button className="bg-white text-slate-900 hover:bg-white/90 font-medium px-8 py-3">
                      {section.uploadText || "Upload Photo"}
                    </Button>
                    <p className="text-white text-sm mt-2 font-medium">Drag & drop or click to upload</p>
              </div>
            </div>
            
                <div className="flex flex-col sm:flex-row gap-4 mt-6">
                  <Button 
                    variant="outline" 
                    className="flex-1 border-slate-300 text-slate-700 hover:bg-slate-50 py-3 font-medium"
                  >
                    {section.resetText || "Reset"}
                  </Button>
                  <Button className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-3">
                    {section.tryOnText || "Try On Style"}
                  </Button>
                </div>
              </div>

              {/* 预览结果区域 */}
              <div className="bg-gradient-to-br from-white to-slate-50 p-8 rounded-2xl border border-slate-200 shadow-lg">
                <h3 className="text-xl font-semibold text-slate-900 mb-4">Preview Result</h3>
                <div className="relative h-64 bg-slate-100 rounded-xl overflow-hidden">
                  <div className="absolute inset-0 flex items-center justify-center text-slate-500">
                    <div className="text-center">
                      <svg className="w-12 h-12 mx-auto mb-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <p className="text-sm font-medium">Your result will appear here</p>
                    </div>
                  </div>
            </div>
          </div>
        </div>
        
            {/* 右侧：发型选择区域 */}
            <div className="bg-gradient-to-br from-white to-slate-50 p-8 rounded-2xl border border-slate-200 shadow-lg">
              <h3 className="text-2xl font-semibold text-slate-900 mb-6 flex items-center gap-3">
                <span className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-sm font-bold text-white">2</span>
                Choose Your Hairstyle
              </h3>
              
              {/* 分类标签 */}
              <div className="flex flex-wrap gap-3 mb-8">
                {categories.map((category, index) => (
                  <Badge 
                    key={`category-${index}`}
                    className={`px-4 py-2 cursor-pointer transition-all font-medium ${
                      selectedCategory === category 
                        ? 'bg-blue-600 text-white hover:bg-blue-700' 
                        : 'bg-slate-200 text-slate-700 hover:bg-slate-300 border-slate-300'
                    }`}
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category}
                  </Badge>
                ))}
              </div>
              
              {/* 发型网格 */}
              <div className="grid grid-cols-2 xl:grid-cols-3 gap-4 max-h-[500px] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-slate-400 scrollbar-track-slate-100">
                {filteredHairstyles
                  .filter(style => style && style.id && style.name)
                  .map((style, index) => (
                  <Card 
                      key={style.id || `style-${index}`}
                      className={`cursor-pointer transition-all duration-300 ${
                        selectedStyle === style.id 
                          ? 'ring-2 ring-blue-500 bg-blue-50' 
                          : 'hover:bg-slate-50 bg-white'
                      } border-slate-200`}
                      onClick={() => setSelectedStyle(style.id)}
                  >
                      <div className="h-36 overflow-hidden rounded-t-lg bg-slate-200 relative">
                        {style.image && !imageErrors.has(style.image) ? (
                          <Image
                        src={style.image} 
                            alt={style.name || 'Hairstyle'}
                            width={140}
                            height={144}
                            className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                            onError={() => handleImageError(style.image)}
                          />
                        ) : (
                          // 显示占位符
                          <div className="w-full h-full flex items-center justify-center bg-slate-200">
                            <div className="text-center text-slate-500">
                              <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                              <p className="text-xs">Image</p>
                            </div>
                          </div>
                        )}
                        {selectedStyle === style.id && (
                          <div className="absolute top-2 right-2 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                    </div>
                      <CardContent className="p-4">
                        <p className="text-sm text-slate-900 font-semibold truncate mb-1">
                          {style.name || 'Unnamed Style'}
                      </p>
                        <p className="text-xs text-blue-600 font-medium">
                          {style.category || 'Uncategorized'}
                      </p>
                    </CardContent>
                  </Card>
                  ))
                }
              </div>

              {/* 选择提示 */}
              {selectedStyle && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-blue-800 text-sm flex items-center gap-2 font-medium">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Style selected! Upload your photo to see the preview.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
