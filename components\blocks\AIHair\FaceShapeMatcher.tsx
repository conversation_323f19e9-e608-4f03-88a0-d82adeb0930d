'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';

interface FaceShape {
  id: string;
  name: string;
  image: string;
  description: string;
  recommendedStyles: string[];
}

interface FaceShapeMatcherProps {
  section: {
    title: string;
    subtitle?: string;
    description?: string;
    shapes: FaceShape[];
  };
}

export default function FaceShapeMatcher({ section }: FaceShapeMatcherProps) {
  const [activeShape, setActiveShape] = useState(section.shapes[0]?.id || '');
  
  return (
    <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
      <div className="p-6 md:p-8 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="text-center max-w-3xl mx-auto mb-10">
          {section.subtitle && (
            <p className="text-lg text-blue-600 font-medium mb-2">
              {section.subtitle}
            </p>
          )}
          
          {section.description && (
            <p className="text-slate-600">
              {section.description}
            </p>
          )}
        </div>
        
        <Tabs 
          defaultValue={activeShape} 
          onValueChange={setActiveShape}
          className="w-full"
        >
          <TabsList className="grid grid-cols-3 md:grid-cols-6 gap-2 bg-transparent h-auto mb-8">
            {section.shapes.map((shape) => (
              <TabsTrigger 
                key={shape.id}
                value={shape.id}
                className="relative flex flex-col items-center p-3 data-[state=active]:bg-white data-[state=active]:shadow-md rounded-lg"
              >
                <div className="w-16 h-16 mb-2 rounded-full overflow-hidden">
                  <img
                    src={shape.image}
                    alt={shape.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-sm font-medium">{shape.name}</span>
              </TabsTrigger>
            ))}
          </TabsList>
          
          {section.shapes.map((shape) => (
            <TabsContent 
              key={shape.id} 
              value={shape.id}
              className="mt-0"
            >
              <div className="bg-white p-6 rounded-xl shadow-sm">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative h-64 md:h-80 rounded-xl overflow-hidden shadow-lg">
                    <img
                      src={shape.image}
                      alt={shape.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <div>
                    <h3 className="text-2xl font-bold mb-4 text-slate-900">{shape.name}</h3>
                    <p className="text-slate-700 mb-6">{shape.description}</p>
                    
                    <div>
                      <h4 className="font-semibold text-slate-900 mb-3">Recommended Styles:</h4>
                      <ul className="space-y-2">
                        {shape.recommendedStyles.map((style, index) => (
                          <li key={index} className="flex items-center">
                            <span className="inline-block w-4 h-4 rounded-full bg-blue-500 mr-3"></span>
                            <span className="text-slate-700">{style}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="mt-8">
                      <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                        View Styles for {shape.name}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          ))}
        </Tabs>
        
        <div className="mt-10 text-center">
          <p className="text-slate-600 mb-4">Not sure about your face shape? Use our AI face shape analyzer</p>
          <Button variant="outline" className="border-blue-500 text-blue-600 hover:bg-blue-50">
            Analyze My Face Shape
          </Button>
        </div>
      </div>
    </div>
  );
}


