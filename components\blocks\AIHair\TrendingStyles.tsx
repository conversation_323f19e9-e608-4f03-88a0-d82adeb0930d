'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface StyleItem {
  id: string;
  title: string;
  description: string;
  image: string;
  tags: string[];
}

interface TrendingStylesProps {
  section: {
    title: string;
    subtitle?: string;
    description?: string;
    items: StyleItem[];
    viewAllLink?: string;
  };
}

export default function TrendingStyles({ section }: TrendingStylesProps) {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  
  return (
    <div className="container mx-auto px-4">
      <div className="text-center max-w-4xl mx-auto mb-16">
        <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
          {section.title}
        </h2>
        
        {section.subtitle && (
          <p className="text-xl text-blue-600 font-medium mb-4">
            {section.subtitle}
          </p>
        )}
        
        {section.description && (
          <p className="text-lg text-slate-600 leading-relaxed">
            {section.description}
          </p>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 lg:gap-8 max-w-7xl mx-auto">
        {section.items.map((item) => (
          <div 
            key={item.id}
            onMouseEnter={() => setHoveredItem(item.id)}
            onMouseLeave={() => setHoveredItem(null)}
            className="flex"
          >
            <Card className="w-full h-full overflow-hidden hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="relative h-48 lg:h-56 overflow-hidden">
                <img
                  src={item.image}
                  alt={item.title}
                  className={`w-full h-full object-cover transition-transform duration-500 ${hoveredItem === item.id ? 'scale-110' : 'scale-100'}`}
                />
                <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/90 via-black/50 to-transparent">
                  <h3 className="text-lg lg:text-xl font-bold text-white drop-shadow-lg">
                    {item.title}
                  </h3>
                </div>
                
                <div className={`absolute top-3 right-3 transition-opacity duration-300 ${hoveredItem === item.id ? 'opacity-100' : 'opacity-0'}`}>
                  <div className="bg-white/90 backdrop-blur-sm rounded-full p-2">
                    <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </div>
                </div>
              </div>
              
              <CardContent className="p-4 lg:p-6 flex flex-col flex-1">
                <p className="text-sm lg:text-base text-slate-700 mb-4 line-clamp-2 flex-1">
                  {item.description}
                </p>
                
                <div className="flex flex-wrap gap-1.5 mb-4">
                  {item.tags.slice(0, 3).map((tag, index) => (
                    <Badge 
                      key={index} 
                      variant="secondary" 
                      className="text-xs bg-blue-50 text-blue-700 hover:bg-blue-100 transition-colors"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                <Button 
                  className={`w-full text-sm transition-all duration-300 ${
                    hoveredItem === item.id 
                      ? 'bg-blue-700 hover:bg-blue-800 shadow-lg transform translate-y-[-1px]' 
                      : 'bg-blue-600 hover:bg-blue-700'
                  } text-white`}
                >
                  <span className="flex items-center justify-center gap-2">
                    Learn More
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </span>
                </Button>
              </CardContent>
            </Card>
          </div>
        ))}
      </div>
      
      {section.viewAllLink && (
        <div className="text-center mt-12 lg:mt-16">
          <p className="text-sm text-slate-500 mb-4">
            Discover more trending men's haircuts in our complete collection
          </p>
          <Link href={section.viewAllLink}>
            <Button 
              variant="outline" 
              size="lg" 
              className="border-blue-500 text-blue-600 hover:bg-blue-50 hover:border-blue-600 transition-all duration-300"
            >
              <span className="flex items-center gap-2">
                View All Trending Styles
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </span>
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}

