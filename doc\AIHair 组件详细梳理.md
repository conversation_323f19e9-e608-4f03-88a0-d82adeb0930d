我来梳理一下 `/components/blocks/AIHair` 目录下的所有组件，分析它们的功能和用途：
Ran tool
## AIHair 组件详细梳理

根据代码分析，这里有 **10 个组件**，我按功能分类整理：

### 🎯 **主要展示组件 (4个)**

1. **`HeroAI.tsx`** - Hero 英雄区域组件
   - 用途：页面顶部的主标题和CTA按钮区域
   - 特色：渐变背景、动画效果、双按钮布局
   - 状态：'use client' (客户端组件)

2. **`PopularHairStyles.tsx`** - 热门发型展示
   - 用途：展示热门发型样式的网格布局
   - 特色：悬停效果、标签系统、View Style 按钮
   - 状态：'use client' (客户端组件)
   - **注意：可能会被 Showcase 组件替代**

3. **`TrendingStyles.tsx`** - 趋势发型展示  
   - 用途：展示趋势发型，类似 PopularHairStyles 但布局不同
   - 特色：4列网格、渐变遮罩、Learn More 按钮
   - 状态：'use client' (客户端组件)

4. **`HairstyleShowcase.tsx`** - 发型展示组件
   - 用途：详细的发型展示，包含难度、维护等级信息
   - 特色：复杂的标签系统、难度颜色编码
   - 状态：'use client' (客户端组件)

### 🧠 **AI 功能组件 (3个)**

5. **`AITryOn.tsx`** - AI 试发型组件
   - 用途：虚拟试发型功能，上传照片试戴不同发型
   - 特色：分类选择、处理状态、结果展示
   - 状态：'use client' (客户端组件)
   - 复杂度：最高 (234行代码)

6. **`FaceShapeMatcher.tsx`** - 脸型匹配器
   - 用途：根据脸型推荐合适的发型
   - 特色：Tab 切换、脸型图示、推荐列表
   - 状态：'use client' (客户端组件)

7. **`FeaturesAI.tsx`** - AI 功能特性展示
   - 用途：展示AI相关功能和特性
   - 特色：左右布局、轮播图、交互式选择
   - 状态：'use client' (客户端组件)

### 📂 **分类和工具组件 (3个)**

8. **`HairstyleCategories.tsx`** - 发型分类展示
   - 用途：展示不同发型分类的入口
   - 特色：大图卡片、悬停效果、数量统计
   - 状态：'use client' (客户端组件)

9. **`HairstyleTools.tsx`** - 发型工具推荐
   - 用途：推荐理发工具和产品
   - 特色：价格显示、评分系统、外链跳转
   - 状态：'use client' (客户端组件)

10. **`StylistShowcase.tsx`** - 造型师展示
    - 用途：展示专业造型师信息
    - 特色：专业信息、专长标签、社交链接
    - 状态：'use client' (客户端组件)

## 📊 **组件使用情况分析**

### **已知使用的组件：**
- ✅ `HeroAI` - 在 buzz-cut 页面使用
- ✅ `PopularHairStyles` - 在多个页面使用 (准备替换为 Showcase)
- ✅ `FeaturesAI` - 在 buzz-cut 页面使用
- ✅ `HairstyleTools` - 在 buzz-cut 页面使用
- ✅ `TrendingStyles` - 在 buzz-cut 页面使用

### **可能冗余的组件：**
- ⚠️ `PopularHairStyles` vs `Showcase` - 功能重叠
- ⚠️ `HairstyleShowcase` vs `PopularHairStyles` - 都是展示发型
- ⚠️ `TrendingStyles` vs `PopularHairStyles` - 布局相似

### **专用功能组件：**
- 🎯 `AITryOn` - 独特的AI试发型功能
- 🎯 `FaceShapeMatcher` - 独特的脸型匹配功能
- 🎯 `StylistShowcase` - 专门展示造型师

## 💡 **优化建议**

1. **统一展示组件**：
   - 保留 `Showcase` (通用)
   - 考虑移除 `PopularHairStyles`
   - 评估 `HairstyleShowcase` 和 `TrendingStyles` 的必要性

2. **保留核心功能**：
   - 保留所有 AI 功能组件 (差异化价值)
   - 保留专用组件 (`HairstyleTools`, `StylistShowcase`)

3. **代码风格统一**：
   - 所有组件都是 'use client'
   - 都使用了 framer-motion 动画
   - 样式风格基本一致

这些组件构成了一个完整的AI发型相关功能生态系统，但存在一定的功能重叠，可以通过统一通用组件来简化维护。