import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

interface HeroAIProps {
  section: {
    title: string;
    subtitle?: string;
    description?: string;
    image?: string;
    primaryAction?: {
      text: string;
      href: string;
    };
    secondaryAction?: {
      text: string;
      href: string;
    };
  };
}

export default function HeroAI({ section }: HeroAIProps) {
  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-slate-900 to-slate-800 py-12 md:py-20">
      {/* 背景图案 */}
      <div className="absolute inset-0 opacity-10">
        <svg className="h-full w-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
              <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" strokeWidth="0.5"/>
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#grid)" />
        </svg>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
          {/* 文字内容 */}
          <div className="flex-1 text-center lg:text-left order-2 lg:order-1">
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 md:mb-6 leading-tight">
              {section.title}
            </h1>
            
            {section.subtitle && (
              <p className="text-lg sm:text-xl md:text-2xl text-blue-300 mb-3 md:mb-4">
                {section.subtitle}
              </p>
            )}
            
            {section.description && (
              <p className="text-base sm:text-lg text-slate-300 mb-6 md:mb-8 max-w-2xl mx-auto lg:mx-0">
                {section.description}
              </p>
            )}
            
            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center lg:justify-start">
              {section.primaryAction && (
                <Link href={section.primaryAction.href}>
                  <Button 
                    size="lg" 
                    className="w-full sm:w-auto bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-medium px-6 md:px-8 py-3"
                  >
                    {section.primaryAction.text}
                  </Button>
                </Link>
              )}
              
              {section.secondaryAction && (
                <Link href={section.secondaryAction.href}>
                  <Button 
                    variant="secondary" 
                    size="lg" 
                    className="w-full sm:w-auto bg-white text-slate-900 hover:bg-slate-100 font-medium px-6 md:px-8 py-3"
                  >
                    {section.secondaryAction.text}
                  </Button>
                </Link>
              )}
            </div>
          </div>
          
          {/* 图片区域 - 修复移动端显示 */}
          {section.image && (
            <div className="flex-1 relative order-1 lg:order-2 w-full">
              <div className="relative h-[250px] sm:h-[300px] md:h-[400px] w-full max-w-[500px] mx-auto">
                <Image
                  src={section.image}
                  alt="AI Hairstyle Preview"
                  width={500}
                  height={400}
                  className="object-cover rounded-2xl shadow-2xl"
                  priority
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 500px"
                />
                {/* 添加一个覆盖层确保图片正确显示 */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl md:rounded-2xl"></div>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}