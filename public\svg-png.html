<!DOCTYPE html>
<html>
<head>
    <title>SVG 转换器</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 600px; 
            margin: 0 auto; 
            padding: 20px;
            background: #f8f9fa;
        }
        .app {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s;
        }
        .upload-area:hover {
            border-color: #6c757d;
        }
        .preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .preview-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .preview-item img {
            width: 64px;
            height: 64px;
            object-fit: contain;
            margin: 10px 0;
        }
        button {
            background: #228be6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #1971c2;
        }
        button:disabled {
            background: #adb5bd;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
        }
        .status.success {
            background: #d3f9d8;
            color: #2b8a3e;
        }
        .status.error {
            background: #ffe3e3;
            color: #c92a2a;
        }
    </style>
</head>
<body>
    <div class="app">
        <h1>SVG 转换器</h1>
    
        <div class="upload-area" id="uploadArea">
            <h3>📁 选择 SVG 文件</h3>
            <input type="file" id="svgFile" accept=".svg" style="margin-bottom: 15px;">
        <div>
                <button id="convertBtn" onclick="convertFiles()" disabled>生成图标</button>
            </div>
            <small style="color: #868e96; display: block; margin-top: 10px;">
                将生成 favicon.ico (256×256) 和 logo.png (1024×1024)
            </small>
        </div>
        
        <div id="preview" class="preview"></div>
    </div>

    <script>
        let svgContent = null;
        const convertBtn = document.getElementById('convertBtn');
        const preview = document.getElementById('preview');

        // 文件上传处理
        document.getElementById('svgFile').addEventListener('change', async function(e) {
            const file = e.target.files[0];
            if (!file) return;
            
            try {
                // 读取SVG文件
                svgContent = await readFileAsText(file);
                
                // 验证SVG内容
                if (!svgContent.includes('<svg')) {
                    throw new Error('无效的 SVG 文件');
                }
                
                // 显示预览
                showStatus('success', 'SVG 文件已加载，点击按钮开始转换');
                convertBtn.disabled = false;
                
                // 显示SVG预览
                showPreview('原始 SVG', svgContent);
                
            } catch (error) {
                showStatus('error', '文件读取失败: ' + error.message);
                convertBtn.disabled = true;
            }
        });

        // 读取文件内容
        function readFileAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = e => resolve(e.target.result);
                reader.onerror = e => reject(new Error('文件读取失败'));
                reader.readAsText(file);
            });
        }

        // 转换文件
        async function convertFiles() {
            if (!svgContent) {
                showStatus('error', '请先选择 SVG 文件');
                return;
            }

            convertBtn.disabled = true;
            preview.innerHTML = '';
            
            try {
                // 生成 favicon.ico
                await generateFavicon();
                
                // 生成 logo.png
                await generateLogo();
                
                showStatus('success', '转换完成！');
            } catch (error) {
                showStatus('error', '转换失败: ' + error.message);
            } finally {
                convertBtn.disabled = false;
            }
        }

        // 生成 Favicon
        async function generateFavicon() {
            const size = 256;
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // 设置画布
            canvas.width = size;
            canvas.height = size;
            
            // 绘制白色背景
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, size, size);
            
            // 绘制SVG
            await drawSVG(ctx, svgContent, size);
            
            // 下载文件
            downloadCanvas(canvas, 'favicon.ico');
            
            // 显示预览
            showPreview('Favicon', canvas.toDataURL(), '256×256');
        }

        // 生成 Logo
        async function generateLogo() {
            const size = 1024;
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
            
            // 设置画布
            canvas.width = size;
            canvas.height = size;
            
            // 透明背景
            ctx.clearRect(0, 0, size, size);
            
            // 绘制SVG
            await drawSVG(ctx, svgContent, size);
            
            // 下载文件
            downloadCanvas(canvas, 'logo.png');
            
            // 显示预览
            showPreview('Logo', canvas.toDataURL(), '1024×1024');
        }

        // 绘制SVG到画布
        function drawSVG(ctx, svgContent, size) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                
                img.onload = () => {
                    // 计算绘制尺寸和位置
                    const padding = size * 0.1; // 10% padding
                    const drawSize = size - (padding * 2);
                    const x = padding;
                    const y = padding;
                    
                    // 绘制图像
                    ctx.drawImage(img, x, y, drawSize, drawSize);
                    resolve();
                };
                
                img.onerror = () => reject(new Error('SVG 渲染失败'));
                
                // 准备SVG数据URL
                const cleanSVG = svgContent
                    .replace(/<\?xml.*?\?>/, '')
                    .replace(/<!DOCTYPE.*?>/, '')
                    .replace(/width=".*?"/, `width="${size}"`)
                    .replace(/height=".*?"/, `height="${size}"`);
                
                const blob = new Blob([cleanSVG], {type: 'image/svg+xml'});
                img.src = URL.createObjectURL(blob);
            });
        }

        // 下载画布内容
        function downloadCanvas(canvas, filename) {
            canvas.toBlob(blob => {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                a.download = filename;
                        a.click();
                        URL.revokeObjectURL(url);
            });
        }
                        
                        // 显示预览
        function showPreview(title, dataUrl, size) {
            const item = document.createElement('div');
            item.className = 'preview-item';
            item.innerHTML = `
                <h4>${title}</h4>
                <img src="${dataUrl}" alt="${title}">
                <p><small>${size}</small></p>
            `;
            preview.appendChild(item);
        }

        // 显示状态信息
        function showStatus(type, message) {
            const status = document.createElement('div');
            status.className = `status ${type}`;
            status.textContent = message;
            
            const existing = preview.querySelector('.status');
            if (existing) {
                preview.removeChild(existing);
            }
            preview.insertBefore(status, preview.firstChild);
        }
    </script>
</body>
</html>