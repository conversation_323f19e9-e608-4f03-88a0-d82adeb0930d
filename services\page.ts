import { LandingPage } from "@/types/pages/landing";
import { DemoPage } from "@/types/pages/demo";
import { BuzzCutPage } from "@/types/pages/buzz-cut";
//import { demoPage } from "@/types/pages/demo";

export async function getLandingPage(locale: string): Promise<LandingPage> {
  try {
    if (locale === "zh-CN") {
      locale = "zh";
    }
    return await import(
      `@/i18n/pages/landing/${locale.toLowerCase()}.json`
    ).then((module) => module.default);
  } catch (error) {
    console.warn(`Failed to load ${locale}.json, falling back to en.json`);
    return await import("@/i18n/pages/landing/en.json").then(
      (module) => module.default as LandingPage
    );
  }
}




//新增子页面

export async function getMenPage(locale: string) {
  try {
    if (locale === "zh-CN") {
      locale = "zh";
    }
    return await import(
      `@/i18n/pages/men/${locale.toLowerCase()}.json`
    ).then((module) => module.default);
  } catch (error) {
    console.warn(`Failed to load men/${locale}.json, falling back to en.json`);
    return await import("@/i18n/pages/men/en.json").then(
      (module) => module.default
    );
  }
}

export async function getUltraShortPage(locale: string) {
  try {
    if (locale === "zh-CN") {
      locale = "zh";
    }
    return await import(
      `@/i18n/pages/men/ultra-short/${locale.toLowerCase()}.json`
    ).then((module) => module.default);
  } catch (error) {
    console.warn(`Failed to load men/ultra-short/${locale}.json, falling back to en.json`);
    return await import("@/i18n/pages/men/ultra-short/en.json").then(
      (module) => module.default
    );
  }
}

// ... existing code ...

export async function getBuzzCutPage(locale: string) {
  try {
    if (locale === "zh-CN") {
      locale = "zh";
    }
    return await import(
      `@/i18n/pages/men/ultra-short/buzz-cut/${locale.toLowerCase()}.json`
    ).then((module) => module.default);
  } catch (error) {
    console.warn(`Failed to load men/ultra-short/buzz-cut/${locale}.json, falling back to en.json`);
    return await import("@/i18n/pages/men/ultra-short/buzz-cut/en.json").then(
      (module) => module.default
    );
  }
}

export async function getMilitaryPage(locale: string) {
  try {
    if (locale === "zh-CN") {
      locale = "zh";
    }
    return await import(
      `@/i18n/pages/men/ultra-short/military/${locale.toLowerCase()}.json`
    ).then((module) => module.default);
  } catch (error) {
    console.warn(`Failed to load men/ultra-short/military/${locale}.json, falling back to en.json`);
    return await import("@/i18n/pages/men/ultra-short/military/en.json").then(
      (module) => module.default
    );
  }
}

export async function getWomenPage(locale: string) {
  try {
    if (locale === "zh-CN") {
      locale = "zh";
    }
    return await import(
      `@/i18n/pages/women/${locale.toLowerCase()}.json`
    ).then((module) => module.default);
  } catch (error) {
    console.warn(`Failed to load women/${locale}.json, falling back to en.json`);
    return await import("@/i18n/pages/women/en.json").then(
      (module) => module.default
    );
  }
}


