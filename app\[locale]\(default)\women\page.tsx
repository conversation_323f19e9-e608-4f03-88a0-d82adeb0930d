// app/[locale]/(default)/men/page.tsx
import { Metadata } from 'next';
import { getWomenPage } from "@/services/page";
import HeroAI from '@/components/blocks/AIHair/HeroAI';
import HairstyleCategories from '@/components/blocks/AIHair/HairstyleCategories';
import PopularHairStyles from '@/components/blocks/AIHair/PopularHairStyles';
import FeaturesAI from '@/components/blocks/AIHair/FeaturesAI';
import TrendingStyles from '@/components/blocks/AIHair/TrendingStyles';
import FaceShapeMatcher from '@/components/blocks/AIHair/FaceShapeMatcher';
import FAQ from "@/components/blocks/faq";
import CTA from "@/components/blocks/cta";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const page = await getWomenPage(locale);
  
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/women`;
  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/women`;
  }

  return {
    title: page.meta.title,
    description: page.meta.description,
    //keywords: page.meta.keywords,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: page.meta.title,
      description: page.meta.description,
      images: ["/imgs/women/hero/womens-hairstyles-hero.webp"],
      url: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
    }
  };
}

export default async function WomensHairstylesPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getWomenPage(locale);

  // 准备组件数据
  const heroData = {
    title: page.hero.title,
    subtitle: page.hero.subtitle,
    description: page.hero.description,
    primaryAction: { text: page.hero.primaryCTA, href: '/#aiTryOn' },
    secondaryAction: { text: page.hero.secondaryCTA, href: '#categories' },
    image: page.hero.image
  };

  const categoriesData = {
    title: page.categories.title,
    subtitle: page.categories.subtitle,
    description: page.categories.description,
    categories: page.categories.items
  };

  const popularStylesData = page.popular.styles;

  const aiFeatures = page.aiFeatures.features.map((feature: any) => ({
    ...feature,
    highlight: feature.id === 'virtual-tryion'
  }));

  const trendingData = [
    { 
      id: "wolf-cut",
      title: "Wolf Cut - Trending Hairstyles for Women", 
      description: "Edgy layered cut among the latest hairstyles for women combining shag and mullet elements for modern texture.",
      image: "/imgs/women/trending/wolf-cut.webp",
      tags: ["Trendy", "Layered", "Modern"]
    },
    { 
      id: "curtain-bangs",
      title: "Curtain Bangs - Face-Framing Haircuts for Women", 
      description: "Soft face-framing bangs perfect for most hairstyles for women, adding feminine touch to any length.",
      image: "/imgs/women/trending/curtain-bangs.webp",
      tags: ["Bangs", "Face-Framing", "Versatile"]
    },
    { 
      id: "butterfly-layers",
      title: "Butterfly Layers - Long Hairstyles for Women", 
      description: "Dramatic layered cut creating volume and movement in long hairstyles for women with face-framing layers.",
      image: "/imgs/women/trending/butterfly-layers.webp",
      tags: ["Layered", "Long", "Voluminous"]
    },
    { 
      id: "textured-bob",
      title: "Textured Bob - Modern Bob Hairstyles for Women", 
      description: "Contemporary twist on classic bob hairstyles for women featuring choppy layers and tousled texture.",
      image: "/imgs/women/trending/textured-bob.webp",
      tags: ["Bob", "Textured", "Contemporary"]
    }
  ];

  return (
    <main className="min-h-screen">
      {/* Hero Section - 包含主要关键词 */}
      <HeroAI section={heroData} />
      
      {/* Introduction Section - 关键词密度优化 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold mb-6 text-slate-900">
              {page.intro.title}
            </h2>
            <div className="text-lg text-slate-600 leading-relaxed">
              <p>{page.intro.content}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section id="categories" className="py-20 bg-slate-50">
        <HairstyleCategories section={categoriesData} />
      </section>

      {/* Popular Styles Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900">
              {page.popular.title}
            </h2>
            <p className="text-xl text-slate-600 mb-4">
              {page.popular.subtitle}
            </p>
            <p className="text-slate-500">
              {page.popular.description}
            </p>
          </div>
          
          <PopularHairStyles styles={popularStylesData} />
        </div>
      </section>

      {/* Age-Specific Styles Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900">
              {page.ageSpecific.title}
            </h2>
            <p className="text-xl text-slate-600 mb-4">
              {page.ageSpecific.subtitle}
            </p>
            <p className="text-slate-500">
              {page.ageSpecific.description}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {page.ageSpecific.groups.map((group: any) => (
              <div key={group.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div className="aspect-w-16 aspect-h-12">
                  <img 
                    src={group.image} 
                    alt={group.title}
                    className="w-full h-48 object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 text-slate-900">{group.title}</h3>
                  <p className="text-slate-600 mb-4">{group.description}</p>
                  <a 
                    href={group.url}
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                  >
                    Explore Styles
                    <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* AI Features Section */}
      {/* <section className="py-20 bg-white">
        <FeaturesAI section={{
          title: page.aiFeatures.title,
          subtitle: page.aiFeatures.subtitle,
          description: page.aiFeatures.description,
          features: aiFeatures
        }} />
      </section> */}

      {/* Trending Styles Section */}
      <section className="py-20 bg-gradient-to-br from-pink-50 to-purple-100">
        <TrendingStyles section={{
          title: page.trends.title,
          subtitle: page.trends.subtitle,
          description: page.trends.description,
          items: trendingData,
          viewAllLink: '/women'
        }} />
      </section>

      {/* Face Shape Matcher */}
      <section className="py-20 bg-white">
        <FaceShapeMatcher section={{
          title: page.faceShape.title,
          subtitle: page.faceShape.subtitle,
          description: page.faceShape.description,
          shapes: []
        }} />
      </section>

      {/* Salon Finder Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900">
              {page.salonFinder.title}
            </h2>
            <p className="text-xl text-slate-600 mb-4">
              {page.salonFinder.subtitle}
            </p>
            <p className="text-slate-500">
              {page.salonFinder.description}
            </p>
          </div>
          
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-slate-900">Find Local Hair Salons</h3>
                  <p className="text-slate-600">Discover skilled stylists specializing in women's hair</p>
                </div>
              </div>
              
              <div className="flex gap-4">
                <input 
                  type="text" 
                  placeholder="Enter your city or zip code"
                  className="flex-1 px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                />
                <button className="px-6 py-3 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors">
                  Search Salons
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <FAQ section={{
            name: 'faq',
            title: page.faq.title,
            description: page.faq.subtitle,
            items: page.faq.items
          }} />
        </div>
      </section>

      {/* Final CTA */}
      <CTA section={{
        name: 'cta',
        title: page.cta.title,
        description: page.cta.description,
        buttons: [
          { title: page.cta.primaryButton, url: '/#aiTryOn' },
          { title: page.cta.secondaryButton, url: '/women' }
        ]
      }} />
    </main>
  );
}