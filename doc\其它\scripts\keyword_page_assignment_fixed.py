import pandas as pd
import numpy as np
import re
from collections import defaultdict
from typing import Dict, List, Tuple

class KeywordPageAssigner:
    """关键词页面分配器 - 将关键词分配到具体页面"""
    
    def __init__(self):
        """初始化页面分配规则"""
        self.initialize_page_rules()
        
    def initialize_page_rules(self):
        """基于网站结构规划初始化页面分配规则"""
        
        # 页面分配规则 - 基于网站结构规划.md
        self.page_assignment_rules = {
            # 首页 - 最高价值关键词
            '/': {
                'keywords': ['buzz cut', 'hair style', 'hairstyles', 'haircuts'],
                'patterns': [r'^(buzz cut|hair style|hairstyles|haircuts)$'],
                'volume_threshold': 5000000,
                'description': '首页 - 核心品牌词',
                'max_keywords': 5
            },
            
            # 男士发型总览
            '/men/': {
                'keywords': ['hair styles men', 'mens haircuts', 'hairstyle for men', 'haircut for men'],
                'patterns': [r'^(hair styles? men|mens? haircuts?|hairstyles? for men|haircuts? for men)$'],
                'volume_threshold': 100000,
                'description': '男士发型总览',
                'max_keywords': 10
            },
            
            # 超短发系列 - buzz cut 主页面
            '/men/ultra-short/buzz-cut/': {
                'keywords': ['buzz cut', 'buzzcut', 'buzz cut for men', 'mens buzz cut', 'masculine buzz cut'],
                'patterns': [r'\bbuzz\s*cut\b(?!.*fade)', r'\bbuzzcut\b', r'\bmens?\s+buzz\s*cut\b'],
                'volume_threshold': 50000,
                'description': 'Buzz Cut 主页面',
                'max_keywords': 15
            },
            
            # buzz cut fade 子页面
            '/men/ultra-short/buzz-cut/fade/': {
                'keywords': ['buzz cut fade', 'buzz cut low fade', 'buzz cut high fade', 'fade buzz cut'],
                'patterns': [r'\bbuzz\s*cut\s+.*fade\b', r'\bfade\s+buzz\s*cut\b'],
                'volume_threshold': 10000,
                'description': 'Buzz Cut Fade 变体',
                'max_keywords': 20
            },
            
            # crew cut 页面
            '/men/ultra-short/crew-cut/': {
                'keywords': ['crew cut', 'crew cut for men', 'crew cut hairstyle', 'crew cut fade'],
                'patterns': [r'\bcrew\s+cut\b'],
                'volume_threshold': 10000,
                'description': 'Crew Cut 发型',
                'max_keywords': 15
            },
            
            # caesar cut 页面
            '/men/ultra-short/caesar-cut/': {
                'keywords': ['caesar cut', 'caesar haircut', 'caesar hairstyle'],
                'patterns': [r'\bcaesar\s+(cut|haircut|hairstyle)\b'],
                'volume_threshold': 10000,
                'description': 'Caesar Cut 发型',
                'max_keywords': 10
            },
            
            # 渐变技术主页面
            '/men/fade/taper-fade/': {
                'keywords': ['taper fade', 'fade haircut', 'fade hairstyle'],
                'patterns': [r'^(taper fade|fade haircut|fade hairstyle)$'],
                'volume_threshold': 100000,
                'description': 'Taper Fade 主页面',
                'max_keywords': 15
            },
            
            # 低渐变页面
            '/men/fade/low-fade/': {
                'keywords': ['low fade', 'low fade haircut', 'low fade hairstyle'],
                'patterns': [r'\blow\s+fade\b'],
                'volume_threshold': 10000,
                'description': 'Low Fade 渐变',
                'max_keywords': 15
            },
            
            # 中渐变页面
            '/men/fade/mid-fade/': {
                'keywords': ['mid fade', 'mid fade haircut', 'medium fade'],
                'patterns': [r'\b(mid|medium)\s+fade\b'],
                'volume_threshold': 10000,
                'description': 'Mid Fade 渐变',
                'max_keywords': 15
            },
            
            # 高渐变页面
            '/men/fade/high-fade/': {
                'keywords': ['high fade', 'high fade haircut', 'high fade hairstyle'],
                'patterns': [r'\bhigh\s+fade\b'],
                'volume_threshold': 10000,
                'description': 'High Fade 渐变',
                'max_keywords': 15
            },
            
            # skin fade 页面
            '/men/fade/skin-fade/': {
                'keywords': ['skin fade', 'skin fade haircut', 'bald fade'],
                'patterns': [r'\b(skin|bald)\s+fade\b'],
                'volume_threshold': 10000,
                'description': 'Skin Fade 渐变',
                'max_keywords': 15
            },
            
            # 男士短发总览
            '/men/short/': {
                'keywords': ['short haircuts for men', 'mens short haircuts', 'short hair men'],
                'patterns': [r'\bshort\s+.*\b(men|mens|male)\b', r'\b(men|mens|male)\s+.*short\b'],
                'volume_threshold': 20000,
                'description': '男士短发总览',
                'max_keywords': 20
            },
            
            # 韩式男发
            '/men/short/korean/': {
                'keywords': ['korean haircut for men', 'korean hairstyle men', 'korean male haircut'],
                'patterns': [r'\bkorean\s+.*\b(men|male|haircut|hairstyle)\b'],
                'volume_threshold': 5000,
                'description': '韩式男士发型',
                'max_keywords': 15
            },
            
            # 商务发型
            '/men/short/professional/': {
                'keywords': ['professional hairstyles men', 'business haircuts men', 'formal haircuts men'],
                'patterns': [r'\b(professional|business|formal)\s+.*\b(men|male)\b'],
                'volume_threshold': 5000,
                'description': '商务男士发型',
                'max_keywords': 15
            },
            
            # mullet 页面
            '/men/specialty/mullet/': {
                'keywords': ['mullet haircut', 'modern mullet', 'mullet hairstyle', 'hairstyle mullet'],
                'patterns': [r'\bmullet\b'],
                'volume_threshold': 50000,
                'description': 'Mullet 发型',
                'max_keywords': 15
            },
            
            # undercut 页面
            '/men/specialty/undercut/': {
                'keywords': ['undercut', 'undercut haircut', 'undercut hairstyle', 'mens undercut'],
                'patterns': [r'\bundercut\b'],
                'volume_threshold': 20000,
                'description': 'Undercut 发型',
                'max_keywords': 15
            },
            
            # 女士发型总览
            '/women/': {
                'keywords': ['hairstyles for women', 'haircut for women', 'womens hairstyles'],
                'patterns': [r'^(hairstyles? for women|haircuts? for women|womens? hairstyles?)$'],
                'volume_threshold': 100000,
                'description': '女士发型总览',
                'max_keywords': 10
            },
            
            # pixie cut 主页面
            '/women/pixie/classic/': {
                'keywords': ['pixie cut', 'pixie haircut', 'pixie hairstyle', 'pixie cuts'],
                'patterns': [r'\bpixie\s+(cut|haircut|hairstyle|cuts)\b', r'^pixie cut$'],
                'volume_threshold': 50000,
                'description': 'Pixie Cut 主页面',
                'max_keywords': 20
            },
            
            # pixie cut 变体
            '/women/pixie/variations/': {
                'keywords': ['long pixie cut', 'short pixie cut', 'pixie with bangs', 'curly pixie'],
                'patterns': [r'\b(long|short|curly)\s+pixie\b', r'\bpixie\s+with\s+bangs\b'],
                'volume_threshold': 5000,
                'description': 'Pixie Cut 变体',
                'max_keywords': 25
            },
            
            # bob cut 主页面
            '/women/bob/classic/': {
                'keywords': ['bob haircut', 'bob cut', 'bob hairstyle', 'bobcut'],
                'patterns': [r'\bbob\s+(haircut|cut|hairstyle)\b', r'^bob cut$', r'\bbobcut\b'],
                'volume_threshold': 50000,
                'description': 'Bob Cut 主页面',
                'max_keywords': 20
            },
            
            # short bob 页面
            '/women/bob/short/': {
                'keywords': ['short bob', 'short bob haircut', 'short bob cut'],
                'patterns': [r'\bshort\s+bob\b'],
                'volume_threshold': 10000,
                'description': 'Short Bob 发型',
                'max_keywords': 15
            },
            
            # 女士短发总览
            '/women/short/': {
                'keywords': ['short hair', 'short haircuts for women', 'short hairstyles for women'],
                'patterns': [r'\bshort\s+hair\b(?!\s+men)', r'\bshort\s+.*\b(women|ladies|female)\b'],
                'volume_threshold': 100000,
                'description': '女士短发总览',
                'max_keywords': 25
            },
            
            # 编发主页面
            '/women/braids/knotless/': {
                'keywords': ['knotless braids', 'goddess braids', 'boho braids'],
                'patterns': [r'\b(knotless|goddess|boho)\s+braids\b'],
                'volume_threshold': 50000,
                'description': '无结编发系列',
                'max_keywords': 20
            },
            
            # 玉米辫页面
            '/women/braids/cornrows/': {
                'keywords': ['cornrows', 'cornrow hairstyles', 'cornrow braids'],
                'patterns': [r'\bcornrows?\b'],
                'volume_threshold': 20000,
                'description': '玉米辫发型',
                'max_keywords': 15
            },
            
            # 编发总览
            '/women/braids/': {
                'keywords': ['braided hairstyles', 'braids', 'braid hairstyles', 'braiding'],
                'patterns': [r'\bbraids?\b(?!\s+for\s+men)', r'\bbraided?\s+hairstyles?\b'],
                'volume_threshold': 20000,
                'description': '编发总览',
                'max_keywords': 30
            },
            
            # 婚礼发型
            '/women/special/wedding/': {
                'keywords': ['wedding hairstyles', 'bridal hairstyles', 'wedding hair'],
                'patterns': [r'\b(wedding|bridal)\s+.*hair\b'],
                'volume_threshold': 10000,
                'description': '婚礼发型',
                'max_keywords': 20
            },
            
            # 美发店定位
            '/salon-finder/': {
                'keywords': ['hair styling near me', 'haircut salon near me', 'barber near me'],
                'patterns': [r'\bnear\s+me\b', r'\bsalon\b', r'\bbarber\b'],
                'volume_threshold': 5000,
                'description': '美发店定位',
                'max_keywords': 50
            },
            
            # 工具产品页面
            '/tools/clippers/': {
                'keywords': ['hair clippers', 'buzz cut clippers', 'hair cutting tools'],
                'patterns': [r'\bclippers?\b', r'\bhair\s+cutting\s+tools\b'],
                'volume_threshold': 5000,
                'description': '理发工具',
                'max_keywords': 30
            },
            
            # AI试发工具
            '/ai-try-on/': {
                'keywords': ['try on hairstyles', 'ai hairstyle', 'hairstyle ai', 'virtual hairstyles'],
                'patterns': [r'\btry\s+on\s+hairstyles\b', r'\bai\s+hairstyle\b', r'\bvirtual\s+hairstyles?\b'],
                'volume_threshold': 1000,
                'description': 'AI虚拟试发',
                'max_keywords': 20
            }
        }
    
    def assign_keyword_to_page(self, keyword: str, search_volume: int, competition: float) -> Tuple[str, str, int]:
        """将关键词分配到最合适的页面"""
        keyword_lower = keyword.lower().strip()
        best_page = '/other/'  # 默认页面
        best_description = '其他页面'
        best_score = 0
        
        for page_url, rules in self.page_assignment_rules.items():
            score = 0
            
            # 检查搜索量阈值
            if search_volume < rules['volume_threshold']:
                continue
            
            # 检查精确关键词匹配
            for exact_keyword in rules['keywords']:
                if keyword_lower == exact_keyword.lower():
                    score += 10  # 精确匹配得分最高
                elif exact_keyword.lower() in keyword_lower:
                    score += 5   # 包含匹配
            
            # 检查正则模式匹配
            for pattern in rules['patterns']:
                if re.search(pattern, keyword_lower, re.IGNORECASE):
                    score += 3
            
            # 更新最佳匹配
            if score > best_score:
                best_score = score
                best_page = page_url
                best_description = rules['description']
        
        return best_page, best_description, best_score

def safe_convert_to_int(value, default=0):
    """安全转换为整数，处理NaN值"""
    try:
        if pd.isna(value) or value == '' or str(value).lower() == 'nan':
            return default
        return int(float(value))
    except (ValueError, TypeError):
        return default

def safe_convert_to_float(value, default=0.0):
    """安全转换为浮点数，处理NaN值"""
    try:
        if pd.isna(value) or value == '' or str(value).lower() == 'nan':
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def clean_keyword(keyword):
    """清理关键词，处理无效数据"""
    if pd.isna(keyword):
        return None
    
    keyword_str = str(keyword).strip()
    
    # 跳过明显无效的关键词
    if (keyword_str == '' or 
        keyword_str.lower() == 'nan' or 
        keyword_str.lower() == 'none' or 
        len(keyword_str) < 2):
        return None
        
    return keyword_str

def create_page_assignment_table(input_file: str, output_file: str):
    """创建关键词页面分配表格"""
    
    print("🚀 开始关键词页面分配...")
    
    # 初始化分配器
    assigner = KeywordPageAssigner()
    
    # 读取关键词文件
    try:
        # 尝试多种编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
        df = None
        
        for encoding in encodings:
            try:
                df = pd.read_csv(input_file, encoding=encoding)
                print(f"✅ 使用 {encoding} 编码成功读取 {len(df)} 个关键词")
                break
            except:
                continue
        
        if df is None:
            print("❌ 无法读取文件")
            return
            
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return
    
    # 数据清理和验证
    print("🧹 开始数据清理...")
    
    # 删除完全空白的行
    df = df.dropna(how='all')
    
    # 显示数据列名
    print(f"📋 数据列名: {list(df.columns)}")
    
    # 分配关键词到页面
    page_assignments = defaultdict(list)
    processed_count = 0
    skipped_count = 0
    error_count = 0
    
    for index, row in df.iterrows():
        try:
            # 清理关键词
            keyword = clean_keyword(row['Keyword'])
            if keyword is None:
                skipped_count += 1
                continue
            
            # 安全转换数值
            search_volume = safe_convert_to_int(row['Avg. monthly searches'], 0)
            competition = safe_convert_to_float(row['Competition (indexed value)'], 0.0)
            
            # 跳过搜索量为0的关键词
            if search_volume == 0:
                skipped_count += 1
                continue
            
            # 分配到页面
            target_page, page_description, match_score = assigner.assign_keyword_to_page(
                keyword, search_volume, competition
            )
            
            # 添加到分配结果
            page_assignments[target_page].append({
                'keyword': keyword,
                'search_volume': search_volume,
                'competition': competition,
                'match_score': match_score,
                'page_description': page_description
            })
            
            processed_count += 1
            
            if processed_count % 500 == 0:
                print(f"⏳ 已处理 {processed_count} 个关键词...")
                
        except Exception as e:
            error_count += 1
            if error_count <= 5:  # 只显示前5个错误
                print(f"⚠️ 处理第{index+1}行时出错: {e}")
            elif error_count == 6:
                print("⚠️ 更多错误已省略...")
            continue
    
    print(f"📊 数据处理完成:")
    print(f"  ✅ 成功处理: {processed_count} 个关键词")
    print(f"  ⏭️ 跳过无效: {skipped_count} 个关键词")
    print(f"  ❌ 处理错误: {error_count} 个关键词")
    
    if processed_count == 0:
        print("❌ 没有有效的关键词数据可处理")
        return
    
    # 生成页面分配表格
    generate_page_assignment_tables(page_assignments, output_file)
    
    print(f"✅ 页面分配完成！")

def generate_page_assignment_tables(page_assignments: Dict, output_file: str):
    """生成页面分配表格（类似截图格式）"""
    
    # 为每个页面生成表格
    all_tables = []
    
    # 按页面重要性排序
    page_priority = {
        '/': 1,
        '/men/': 2,
        '/women/': 3,
        '/men/ultra-short/buzz-cut/': 4,
        '/men/fade/taper-fade/': 5,
        '/women/pixie/classic/': 6,
        '/women/bob/classic/': 7,
        '/salon-finder/': 8
    }
    
    sorted_pages = sorted(page_assignments.items(), 
                         key=lambda x: page_priority.get(x[0], 999))
    
    for page_url, keywords_data in sorted_pages:
        if not keywords_data:
            continue
            
        # 按搜索量排序关键词
        keywords_data.sort(key=lambda x: x['search_volume'], reverse=True)
        
        # 限制关键词数量（根据页面规则）
        assigner = KeywordPageAssigner()
        max_keywords = assigner.page_assignment_rules.get(page_url, {}).get('max_keywords', 50)
        keywords_data = keywords_data[:max_keywords]
        
        # 生成表格数据
        table_data = []
        page_description = keywords_data[0]['page_description'] if keywords_data else '未知页面'
        
        # 主关键词（搜索量最高的）
        if keywords_data:
            main_keyword = keywords_data[0]
            table_data.append({
                '主关键词': main_keyword['keyword'],
                '搜索量': format_search_volume(main_keyword['search_volume']),
                'KD值': int(main_keyword['competition']),
                '页面': page_url,
                '话题': page_description,
                '大纲': ''
            })
            
            # 次级关键词
            for keyword_data in keywords_data[1:]:
                table_data.append({
                    '主关键词': '',  # 空白，表示是次级关键词
                    '搜索量': format_search_volume(keyword_data['search_volume']),
                    'KD值': int(keyword_data['competition']),
                    '页面': '',
                    '话题': keyword_data['keyword'],
                    '大纲': ''
                })
        
        all_tables.append({
            'page_url': page_url,
            'page_description': page_description,
            'table_data': table_data,
            'keyword_count': len(keywords_data),
            'total_volume': sum(kd['search_volume'] for kd in keywords_data)
        })
    
    # 保存到CSV文件
    save_assignment_results(all_tables, output_file)
    
    # 生成汇总报告
    generate_assignment_summary(all_tables, output_file.replace('.csv', '_summary.txt'))

def format_search_volume(volume: int) -> str:
    """格式化搜索量显示"""
    if volume >= 1000000:
        return f"{volume//1000000}M"
    elif volume >= 1000:
        return f"{volume//1000}K"
    else:
        return str(volume)

def save_assignment_results(all_tables: List, output_file: str):
    """保存分配结果到CSV"""
    
    # 准备CSV数据
    csv_data = []
    
    for table in all_tables:
        # 添加页面标题行
        csv_data.append({
            '主关键词': f"=== {table['page_description']} ({table['page_url']}) ===",
            '搜索量': f"共{table['keyword_count']}个词",
            'KD值': f"总量{format_search_volume(table['total_volume'])}",
            '页面': '',
            '话题': '',
            '大纲': ''
        })
        
        # 添加关键词数据
        for row in table['table_data']:
            csv_data.append(row)
        
        # 添加空行分隔
        csv_data.append({
            '主关键词': '',
            '搜索量': '',
            'KD值': '',
            '页面': '',
            '话题': '',
            '大纲': ''
        })
    
    # 保存到CSV
    result_df = pd.DataFrame(csv_data)
    result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"📊 页面分配表格已保存到: {output_file}")

def generate_assignment_summary(all_tables: List, summary_file: str):
    """生成分配汇总报告"""
    
    report = []
    report.append("📊 关键词页面分配汇总报告")
    report.append("=" * 60)
    
    total_keywords = sum(table['keyword_count'] for table in all_tables)
    total_volume = sum(table['total_volume'] for table in all_tables)
    
    report.append(f"\n📈 总体统计:")
    report.append(f"  总关键词数: {total_keywords}")
    report.append(f"  总搜索量: {format_search_volume(total_volume)}")
    report.append(f"  分配页面数: {len(all_tables)}")
    
    report.append(f"\n🏆 页面分配详情:")
    
    # 按总搜索量排序
    sorted_tables = sorted(all_tables, key=lambda x: x['total_volume'], reverse=True)
    
    for i, table in enumerate(sorted_tables, 1):
        if total_volume > 0:
            percentage = (table['total_volume'] / total_volume) * 100
        else:
            percentage = 0
        report.append(f"  {i}. {table['page_description']}")
        report.append(f"     页面: {table['page_url']}")
        report.append(f"     关键词数: {table['keyword_count']}")
        report.append(f"     搜索量: {format_search_volume(table['total_volume'])} ({percentage:.1f}%)")
        report.append("")
    
    # 保存报告
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print(f"📋 汇总报告已保存到: {summary_file}")

if __name__ == "__main__":
    # 使用示例
    input_file = "doc/谷歌关键词规划+综合.csv"
    output_file = "doc/关键词页面分配表.csv"
    
    create_page_assignment_table(input_file, output_file) 