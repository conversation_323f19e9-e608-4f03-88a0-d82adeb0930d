import { Metadata } from 'next';
import { getLandingPage } from '@/services/page';
import Feature1 from '@/components/blocks/feature1';
// import FAQ from '@/components/blocks/faq';
// import CTA from '@/components/blocks/cta';
// import Showcase from '@/components/blocks/showcase';
// import FeaturesAI from '@/components/blocks/AIHair/FeaturesAI';
// 导入 HeroAI 而不是传统 Hero
import HeroAI from '@/components/blocks/AIHair/HeroAI';
import dynamic from 'next/dynamic';

// 1. 设置ISR，页面每小时重新生成一次，极大提高TTFB
export const revalidate = 3600; 

// 2. 动态加载非首屏组件，减少初始JS负载
const Showcase = dynamic(() => import('@/components/blocks/showcase'), {
  loading: () => <div className="h-[500px]" />, // 使用占位符防止布局抖动
});
const AITryOn = dynamic(() => import('@/components/blocks/AIHair/AITryOn'), {
  loading: () => <div className="h-[600px]" />,
});
const FAQ = dynamic(() => import('@/components/blocks/faq'), {
  loading: () => <div className="h-[400px]" />,
});
const CTA = dynamic(() => import('@/components/blocks/cta'), {
  loading: () => <div className="h-[300px]" />,
});
// 移除未使用的 FeaturesAI 导入
// import FeaturesAI from '@/components/blocks/AIHair/FeaturesAI';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    title: "AI Buzz Cut Simulator & Hair Style Try-On | BuzzcutAI ",
    description: "Upload your photo for a virtual haircut try-on! Our AI simulator helps men & women explore trendy short haircuts, buzz cuts, and pixie styles to find your perfect look.",
    //keywords: "buzz cut, hair style, haircuts, gents hair style, mens haircuts, buzz cut for men",
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: "Buzz Cut & Hair Style AI Guide 2025 ",
      description: "Transform your look with AI buzz cut recommendations and hair style analysis. Get personalized haircuts for men, try gents hair styles, and find hair styling services near you.",
      url: canonicalUrl,
      siteName: "BuzzcutAI.hair",
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/images/buzz-cut-og.jpg`,
          width: 1200,
          height: 630,
          alt: "AI Hair Styling Platform",
        },
      ],
      locale: locale,
      type: "website",
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLandingPage(locale) as any;

  // 转换 hero 数据为 HeroAI 需要的格式
  const heroAIData = page.hero ? {
    title: page.hero.title,
    subtitle: "Experience the Future of Hair Styling",
    description: page.hero.description,
    image: "/imgs/hero-buzz-cut-showcase.jpg",
    primaryAction: {
      text: page.hero.primaryCta || "Try AI Virtual Hairstyling",
      href: "#aiTryOn"
    },
    secondaryAction: {
      text: page.hero.secondaryCta || "Explore Hair Styles", 
      href: "#popularStyles"
    }
  } : null;

  return (
    <main className="min-h-screen">
      {/* 使用 HeroAI 组件 */}
      {heroAIData && <HeroAI section={heroAIData} />}

      {/* 特性介绍 - 立即加载 */}
      {page.feature1 && <Feature1 section={page.feature1} />}

      {/* 以下为非首屏内容，动态加载 */}
      {page.popularStyles && (
        <section id="popularStyles">
          <Showcase section={page.popularStyles} />
        </section>
      )} 
      {page.aiTryOn && (
        <section id="aiTryOn">
          <AITryOn section={page.aiTryOn} />
        </section>
      )}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </main>
  );
}
