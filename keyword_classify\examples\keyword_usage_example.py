"""
关键词分类框架使用示例
Author: AI Assistant
Description: 展示如何在其他项目中复用通用关键词分类算法
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.keyword_classifier_framework import (
    KeywordClassificationFramework,
    ClassificationRule,
    DataProcessor,
    ResultExporter,
    KeywordData
)


def example_ecommerce_classification():
    """电商网站关键词分类示例"""
    
    print("🛒 电商网站关键词分类示例")
    print("=" * 50)
    
    # 1. 创建分类框架
    classifier = KeywordClassificationFramework()
    
    # 2. 定义电商分类规则
    
    # 手机类别
    mobile_rule = ClassificationRule(
        page_url="/electronics/mobile/",
        description="手机专区",
        exact_keywords=["iphone", "samsung", "xiaomi", "mobile", "smartphone"],
        regex_patterns=[r'\biphone\b', r'\bphone\b', r'mobile', r'智能手机'],
        volume_threshold=100,
        max_keywords=100,
        priority=1
    )
    classifier.add_rule(mobile_rule)
    
    # 服装类别
    clothing_rule = ClassificationRule(
        page_url="/fashion/clothing/",
        description="服装专区",
        exact_keywords=["dress", "shirt", "pants", "shoes", "clothing"],
        regex_patterns=[r'\bdress\b', r'\bshirt\b', r'\bclothing\b'],
        volume_threshold=50,
        max_keywords=150,
        priority=2
    )
    classifier.add_rule(clothing_rule)
    
    # 兜底规则
    fallback_rule = ClassificationRule(
        page_url="/other/",
        description="其他商品",
        regex_patterns=[r'.*'],
        volume_threshold=1,
        max_keywords=10000,
        priority=999
    )
    classifier.set_fallback_rule(fallback_rule)
    
    # 3. 模拟关键词数据
    test_keywords = [
        KeywordData("iphone 15", 1000000, 8.5),
        KeywordData("samsung galaxy", 800000, 7.2),
        KeywordData("summer dress", 500000, 6.0),
        KeywordData("nike shoes", 700000, 7.8),
        KeywordData("laptop computer", 600000, 8.0),
    ]
    
    # 4. 执行分类
    results = classifier.classify_batch(test_keywords)
    
    # 5. 输出结果
    print("\n📊 分类结果:")
    for result in results:
        print(f"关键词: {result.keyword}")
        print(f"  目标页面: {result.target_page}")
        print(f"  页面描述: {result.page_description}")
        print(f"  匹配分数: {result.match_score:.1f}")
        print(f"  置信度: {result.confidence:.2f}")
        print()
    
    # 6. 获取统计信息
    stats = classifier.get_statistics()
    print("📈 分类统计:")
    print(f"总处理数: {stats['total_processed']}")
    print(f"成功分类: {stats['total_assigned']}")
    print(f"覆盖率: {stats['coverage_rate']:.1%}")
    
    return results


def main():
    """主函数 - 展示框架使用方法"""
    
    print("🔧 通用关键词分类框架使用示例")
    print("=" * 60)
    
    # 运行示例
    example_ecommerce_classification()
    
    print("\n✅ 示例执行完成!")
    print("\n📚 框架使用指南:")
    print("1. 导入框架: from utils.keyword_classifier_framework import *")
    print("2. 创建分类器: classifier = KeywordClassificationFramework()")
    print("3. 定义规则: rule = ClassificationRule(...)")
    print("4. 添加规则: classifier.add_rule(rule)")
    print("5. 执行分类: results = classifier.classify_batch(data)")
    print("6. 导出结果: ResultExporter.export_to_csv(results, 'output.csv')")
    
    print("\n🎯 框架特点:")
    print("• 🔧 高度可配置：支持精确匹配、正则匹配、搜索量阈值")
    print("• 📊 智能评分：基于匹配度、优先级的综合评分系统")
    print("• 🚀 批量处理：支持大规模关键词批量分类")
    print("• 📈 统计分析：提供详细的分类统计和覆盖率分析")
    print("• 💾 多种导出：支持表格格式和平铺格式导出")


if __name__ == "__main__":
    main() 