import { Header } from "@/types/blocks/header";
import { Footer } from "@/types/blocks/footer";

// 复用相同的PageMetadata类型
export interface PageMetadata {
  title?: string;
  description?: string;
  keywords?: string;
}

// Buzz Cut 发型风格项目类型
export interface BuzzCutStyleItem {
  id: string;
  title: string;
  description: string;
  image: string;
  tags: string[];
  difficulty: 'Easy' | 'Medium' | 'Hard';
  maintenanceFrequency: string;
}

// 发型风格部分类型
export interface BuzzCutStyles {
  title: string;
  subtitle: string;
  description: string;
  categories: Array<{
    id: string;
    name: string;
    description: string;
    styles: BuzzCutStyleItem[];
  }>;
}

// 护理指南类型
export interface CareGuide {
  title: string;
  steps: Array<{
    step: number;
    title: string;
    description: string;
    image?: string;
  }>;
}

// 工具推荐类型
export interface ToolRecommendation {
  id: string;
  name: string;
  type: 'Clipper' | 'Trimmer' | 'Comb' | 'Product';
  description: string;
  image: string;
  price: string;
  rating: number;
  features: string[];
  buyUrl: string;
}

// Buzz Cut 页面结构
export interface BuzzCutPage {
  metadata?: PageMetadata;
  header?: Header;
  hero: {
    title: string;
    subtitle: string;
    description: string;
    primaryCta: string;
    secondaryCta: string;
    backgroundImage: string;
  };
  intro: {
    title: string;
    description: string;
    benefits: string[];
    image: string;
    history?: string;
  };
  styles: BuzzCutStyles;
  faceShapeGuide: {
    title: string;
    description: string;
    faceShapes: Array<{
      shape: string;
      description: string;
      recommendedStyles: string[];
      image: string;
    }>;
  };
  careGuide: CareGuide;
  tools: {
    title: string;
    description: string;
    essential: ToolRecommendation[];
    optional: ToolRecommendation[];
  };
  gallery: {
    title: string;
    description: string;
    images: Array<{
      id: string;
      title: string;
      image: string;
      style: string;
      faceShape: string;
    }>;
  };
  faq: {
    title: string;
    items: Array<{
      question: string;
      answer: string;
    }>;
  };
  cta: {
    title: string;
    description: string;
    primaryButton: {
      text: string;
      url: string;
    };
    secondaryButton: {
      text: string;
      url: string;
    };
  };
  footer?: Footer;
} 