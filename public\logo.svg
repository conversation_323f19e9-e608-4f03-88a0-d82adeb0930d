<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <!-- AI科技渐变 -->
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#a855f7;stop-opacity:1" />
    </linearGradient>
    
    <!-- 发型工具渐变 -->
    <linearGradient id="toolGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>

    <!-- AI光效 -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 背景圆形 -->
  <circle cx="50" cy="50" r="45" fill="url(#aiGradient)" opacity="0.1"/>
  
  <!-- 主要图标：头部轮廓 + AI元素 -->
  <g transform="translate(15, 10) scale(1.4)">
    <!-- 头部轮廓 -->
    <path d="M22 8 C28 8, 32 12, 32 18 C32 24, 28 32, 22 36 L22 42 C22 43, 21 44, 20 44 L16 44 C15 44, 14 43, 14 42 L14 36 C8 32, 4 24, 4 18 C4 12, 8 8, 14 8 Z" 
          fill="url(#aiGradient)" opacity="0.8"/>
    
    <!-- Buzz Cut发型线条 -->
    <path d="M14 8 L22 8 C24 8, 26 10, 26 12 L26 16 C26 18, 24 20, 22 20 L14 20 C12 20, 10 18, 10 16 L10 12 C10 10, 12 8, 14 8 Z" 
          fill="none" stroke="url(#toolGradient)" stroke-width="2" opacity="0.9"/>
    
    <!-- AI电路纹理 -->
    <g stroke="url(#aiGradient)" stroke-width="1" fill="none" opacity="0.6">
      <path d="M12 14 L16 14 M18 14 L22 14"/>
      <path d="M14 12 L14 16 M20 12 L20 16"/>
      <circle cx="16" cy="14" r="1" fill="url(#aiGradient)"/>
      <circle cx="20" cy="14" r="1" fill="url(#aiGradient)"/>
    </g>
    
    <!-- 虚拟扫描线 -->
    <g stroke="url(#aiGradient)" stroke-width="0.5" opacity="0.4">
      <path d="M6 22 L30 22" stroke-dasharray="2,1">
        <animate attributeName="stroke-dasharray" values="2,1;4,2;2,1" dur="2s" repeatCount="indefinite"/>
      </path>
      <path d="M6 26 L30 26" stroke-dasharray="3,2">
        <animate attributeName="stroke-dasharray" values="3,2;6,3;3,2" dur="1.5s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- 理发工具图标 -->
    <g transform="translate(26, 6)">
      <!-- 剪刀 -->
      <path d="M2 2 L6 6 M6 2 L2 6" stroke="url(#toolGradient)" stroke-width="1.5" fill="none" opacity="0.7"/>
      <circle cx="2" cy="2" r="1" fill="url(#toolGradient)" opacity="0.7"/>
      <circle cx="6" cy="6" r="1" fill="url(#toolGradient)" opacity="0.7"/>
    </g>
  </g>
  
  <!-- AI光效动画 -->
  <g filter="url(#glow)">
    <circle cx="45" cy="15" r="1" fill="#6366f1" opacity="0.8">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="48" cy="18" r="0.5" fill="#8b5cf6" opacity="0.6">
      <animate attributeName="opacity" values="0.2;0.6;0.2" dur="1.5s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>