"""
关键词分类框架使用示例
Author: AI Assistant
Description: 展示如何在其他项目中复用通用关键词分类算法
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.keyword_classifier_framework import (
    KeywordClassificationFramework,
    ClassificationRule,
    DataProcessor,
    ResultExporter,
    KeywordData
)


def example_ecommerce_classification():
    """电商网站关键词分类示例"""
    
    print("🛒 电商网站关键词分类示例")
    print("=" * 50)
    
    # 1. 创建分类框架
    classifier = KeywordClassificationFramework()
    
    # 2. 定义电商分类规则
    
    # 手机类别
    mobile_rule = ClassificationRule(
        page_url="/electronics/mobile/",
        description="手机专区",
        exact_keywords=["iphone", "samsung", "xiaomi", "mobile", "smartphone"],
        regex_patterns=[r'\biphone\b', r'\bphone\b', r'mobile', r'智能手机'],
        volume_threshold=100,
        max_keywords=100,
        priority=1
    )
    classifier.add_rule(mobile_rule)
    
    # 服装类别
    clothing_rule = ClassificationRule(
        page_url="/fashion/clothing/",
        description="服装专区",
        exact_keywords=["dress", "shirt", "pants", "shoes", "clothing"],
        regex_patterns=[r'\bdress\b', r'\bshirt\b', r'\bclothing\b'],
        volume_threshold=50,
        max_keywords=150,
        priority=2
    )
    classifier.add_rule(clothing_rule)
    
    # 美妆类别
    beauty_rule = ClassificationRule(
        page_url="/beauty/cosmetics/",
        description="美妆专区",
        exact_keywords=["lipstick", "foundation", "makeup", "skincare"],
        regex_patterns=[r'\bmakeup\b', r'\bbeauty\b', r'\bcosmetics\b'],
        volume_threshold=30,
        max_keywords=80,
        priority=3
    )
    classifier.add_rule(beauty_rule)
    
    # 兜底规则
    fallback_rule = ClassificationRule(
        page_url="/other/",
        description="其他商品",
        regex_patterns=[r'.*'],
        volume_threshold=1,
        max_keywords=10000,
        priority=999
    )
    classifier.set_fallback_rule(fallback_rule)
    
    # 3. 模拟关键词数据
    test_keywords = [
        KeywordData("iphone 15", 1000000, 8.5),
        KeywordData("samsung galaxy", 800000, 7.2),
        KeywordData("summer dress", 500000, 6.0),
        KeywordData("nike shoes", 700000, 7.8),
        KeywordData("red lipstick", 300000, 5.5),
        KeywordData("makeup tutorial", 400000, 4.2),
        KeywordData("laptop computer", 600000, 8.0),  # 这个会被分到其他类别
    ]
    
    # 4. 执行分类
    results = classifier.classify_batch(test_keywords)
    
    # 5. 输出结果
    print("\n📊 分类结果:")
    for result in results:
        print(f"关键词: {result.keyword}")
        print(f"  目标页面: {result.target_page}")
        print(f"  页面描述: {result.page_description}")
        print(f"  匹配分数: {result.match_score:.1f}")
        print(f"  置信度: {result.confidence:.2f}")
        print()
    
    # 6. 获取统计信息
    stats = classifier.get_statistics()
    print("📈 分类统计:")
    print(f"总处理数: {stats['total_processed']}")
    print(f"成功分类: {stats['total_assigned']}")
    print(f"覆盖率: {stats['coverage_rate']:.1%}")
    
    # 7. 导出结果
    ResultExporter.export_to_csv(results, "examples/ecommerce_classification.csv", "flat")
    print("结果已导出到: examples/ecommerce_classification.csv")


def example_travel_classification():
    """旅游网站关键词分类示例"""
    
    print("\n✈️ 旅游网站关键词分类示例")
    print("=" * 50)
    
    classifier = KeywordClassificationFramework()
    
    # 目的地分类
    destination_rule = ClassificationRule(
        page_url="/destinations/",
        description="旅游目的地",
        exact_keywords=["paris", "london", "tokyo", "beijing", "new york"],
        regex_patterns=[r'\bparis\b', r'\blondon\b', r'\btokyo\b', r'\bbeijing\b'],
        volume_threshold=50,
        max_keywords=200,
        priority=1
    )
    classifier.add_rule(destination_rule)
    
    # 酒店分类
    hotel_rule = ClassificationRule(
        page_url="/hotels/",
        description="酒店预订",
        exact_keywords=["hotel", "accommodation", "resort", "booking"],
        regex_patterns=[r'\bhotel\b', r'\bresort\b', r'\bbooking\b'],
        volume_threshold=100,
        max_keywords=150,
        priority=2
    )
    classifier.add_rule(hotel_rule)
    
    # 机票分类
    flight_rule = ClassificationRule(
        page_url="/flights/",
        description="机票预订",
        exact_keywords=["flight", "airline", "ticket", "airfare"],
        regex_patterns=[r'\bflight\b', r'\bairline\b', r'\bticket\b'],
        volume_threshold=80,
        max_keywords=100,
        priority=3
    )
    classifier.add_rule(flight_rule)
    
    # 兜底规则
    fallback_rule = ClassificationRule(
        page_url="/other/",
        description="其他旅游服务",
        regex_patterns=[r'.*'],
        volume_threshold=1,
        max_keywords=10000,
        priority=999
    )
    classifier.set_fallback_rule(fallback_rule)
    
    # 测试数据
    test_keywords = [
        KeywordData("paris vacation", 800000, 7.0),
        KeywordData("london hotel", 600000, 8.5),
        KeywordData("flight to tokyo", 500000, 6.8),
        KeywordData("resort booking", 300000, 5.5),
        KeywordData("airline tickets", 400000, 7.2),
        KeywordData("travel insurance", 200000, 4.0),  # 其他类别
    ]
    
    results = classifier.classify_batch(test_keywords)
    
    print("\n📊 旅游分类结果:")
    for result in results:
        print(f"{result.keyword} → {result.page_description} ({result.target_page})")
    
    return results


def example_config_driven_classification():
    """基于配置文件的分类示例"""
    
    print("\n⚙️ 配置驱动的分类示例")
    print("=" * 50)
    
    # 创建配置文件
    config = {
        "rules": [
            {
                "page_url": "/tech/ai/",
                "description": "人工智能",
                "exact_keywords": ["ai", "artificial intelligence", "machine learning", "deep learning"],
                "regex_patterns": [r"\bai\b", r"machine learning", r"deep learning"],
                "volume_threshold": 100,
                "max_keywords": 50,
                "priority": 1
            },
            {
                "page_url": "/tech/blockchain/",
                "description": "区块链",
                "exact_keywords": ["blockchain", "bitcoin", "cryptocurrency", "nft"],
                "regex_patterns": [r"\bblockchain\b", r"\bbitcoin\b", r"\bcrypto\b"],
                "volume_threshold": 80,
                "max_keywords": 40,
                "priority": 2
            }
        ],
        "fallback_rule": {
            "page_url": "/tech/other/",
            "description": "其他技术",
            "regex_patterns": [r".*"],
            "volume_threshold": 1,
            "max_keywords": 10000,
            "priority": 999
        }
    }
    
    # 保存配置到文件
    import json
    with open("examples/tech_classification_config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    # 使用配置文件创建分类器
    classifier = KeywordClassificationFramework()
    # classifier.load_rules_from_config("examples/tech_classification_config.json")
    
    # 手动添加规则（模拟配置加载）
    for rule_config in config["rules"]:
        rule = ClassificationRule(**rule_config)
        classifier.add_rule(rule)
    
    fallback_config = config["fallback_rule"]
    fallback_rule = ClassificationRule(**fallback_config)
    classifier.set_fallback_rule(fallback_rule)
    
    # 测试数据
    test_keywords = [
        KeywordData("artificial intelligence tutorial", 500000, 6.5),
        KeywordData("machine learning python", 400000, 7.0),
        KeywordData("bitcoin price", 800000, 8.0),
        KeywordData("blockchain technology", 300000, 5.5),
        KeywordData("javascript framework", 200000, 4.0),  # 其他类别
    ]
    
    results = classifier.classify_batch(test_keywords)
    
    print("\n📊 配置驱动分类结果:")
    for result in results:
        print(f"{result.keyword} → {result.page_description}")
    
    print(f"配置文件已保存到: examples/tech_classification_config.json")


def example_multi_language_classification():
    """多语言关键词分类示例"""
    
    print("\n🌍 多语言关键词分类示例")
    print("=" * 50)
    
    classifier = KeywordClassificationFramework()
    
    # 中文规则
    chinese_rule = ClassificationRule(
        page_url="/cn/food/",
        description="中文美食",
        exact_keywords=["火锅", "麻辣烫", "小龙虾", "烧烤"],
        regex_patterns=[r'火锅', r'麻辣烫', r'川菜', r'粤菜'],
        volume_threshold=50,
        max_keywords=100,
        priority=1
    )
    classifier.add_rule(chinese_rule)
    
    # 英文规则
    english_rule = ClassificationRule(
        page_url="/en/food/",
        description="English Food",
        exact_keywords=["pizza", "burger", "pasta", "salad"],
        regex_patterns=[r'\bpizza\b', r'\bburger\b', r'\bpasta\b'],
        volume_threshold=50,
        max_keywords=100,
        priority=2
    )
    classifier.add_rule(english_rule)
    
    # 兜底规则
    fallback_rule = ClassificationRule(
        page_url="/other/",
        description="其他语言",
        regex_patterns=[r'.*'],
        volume_threshold=1,
        max_keywords=10000,
        priority=999
    )
    classifier.set_fallback_rule(fallback_rule)
    
    # 多语言测试数据
    test_keywords = [
        KeywordData("北京火锅", 300000, 5.0),
        KeywordData("四川麻辣烫", 200000, 4.5),
        KeywordData("italian pizza", 500000, 6.0),
        KeywordData("cheese burger", 400000, 7.0),
        KeywordData("寿司", 150000, 3.5),  # 日语，会被分到其他类别
    ]
    
    results = classifier.classify_batch(test_keywords)
    
    print("\n📊 多语言分类结果:")
    for result in results:
        print(f"{result.keyword} → {result.page_description} ({result.target_page})")


def main():
    """主函数 - 运行所有示例"""
    
    print("🔧 通用关键词分类框架使用示例")
    print("=" * 60)
    
    # 确保examples目录存在
    os.makedirs("examples", exist_ok=True)
    
    # 运行各种示例
    example_ecommerce_classification()
    example_travel_classification()
    example_config_driven_classification()
    example_multi_language_classification()
    
    print("\n✅ 所有示例执行完成!")
    print("\n📚 框架使用指南:")
    print("1. 导入框架: from utils.keyword_classifier_framework import *")
    print("2. 创建分类器: classifier = KeywordClassificationFramework()")
    print("3. 定义规则: rule = ClassificationRule(...)")
    print("4. 添加规则: classifier.add_rule(rule)")
    print("5. 执行分类: results = classifier.classify_batch(data)")
    print("6. 导出结果: ResultExporter.export_to_csv(results, 'output.csv')")
    
    print("\n🎯 框架特点:")
    print("• 🔧 高度可配置：支持精确匹配、正则匹配、搜索量阈值")
    print("• 📊 智能评分：基于匹配度、优先级的综合评分系统")
    print("• 🚀 批量处理：支持大规模关键词批量分类")
    print("• 📈 统计分析：提供详细的分类统计和覆盖率分析")
    print("• 💾 多种导出：支持表格格式和平铺格式导出")
    print("• 🌍 多语言：支持中英文等多语言关键词处理")
    print("• ⚙️ 配置驱动：支持JSON配置文件驱动的规则管理")


if __name__ == "__main__":
    main() 