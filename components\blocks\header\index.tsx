"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button, buttonVariants } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

import { Header as HeaderType } from "@/types/blocks/header";
import Icon from "@/components/icon";
import Link from "next/link";
import LocaleToggle from "@/components/locale/toggle";
import { Menu } from "lucide-react";
import SignToggle from "@/components/sign/toggle";
import ThemeToggle from "@/components/theme/toggle";
import { cn } from "@/lib/utils";
import React from "react";

export default function Header({ header }: { header: HeaderType }) {
  if (header.disabled) {
    return null;
  }

  return (
    <section className="py-3 relative z-50 bg-white dark:bg-slate-900">
      <div className="md:max-w-7xl mx-auto px-4">
        <nav className="hidden justify-between lg:flex">
          <div className="flex items-center gap-6">
            <a
              href={header.brand?.url || ""}
              className="flex items-center gap-2"
            >
              {header.brand?.logo?.src && (
                <img
                  src={header.brand.logo.src}
                  alt={header.brand.logo.alt || header.brand.title}
                  className="w-8"
                />
              )}
              {header.brand?.title && (
                <span className="text-xl text-primary font-bold">
                  {header.brand?.title || ""}
                </span>
              )}
            </a>
            <div className="flex items-center">
              <NavigationMenu>
                <NavigationMenuList>
                  {header.nav?.items?.map((item, i) => {
                    if (item.children && item.children.length > 0) {
                      return (
                        <NavigationMenuItem
                          key={i}
                          className="text-muted-foreground"
                        >
                          <NavigationMenuTrigger>
                            {item.icon && (
                              <Icon
                                name={item.icon}
                                className="size-4 shrink-0 mr-2"
                              />
                            )}
                            <span>{item.title}</span>
                          </NavigationMenuTrigger>
                          <NavigationMenuContent>
                            <ul className="w-80 p-4 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 shadow-xl rounded-lg">
                              {item.children.map((iitem, ii) => {
                                if (iitem.children && iitem.children.length > 0) {
                                  return (
                                    <React.Fragment key={ii}>
                                      <li>
                                        <NavigationMenuLink 
                                          href={iitem.url}
                                          className={cn(
                                            "flex select-none gap-4 rounded-lg p-3 leading-none no-underline outline-none transition-colors",
                                            "hover:bg-slate-100 dark:hover:bg-slate-700",
                                            "focus:bg-slate-100 dark:focus:bg-slate-700"
                                          )}
                                        >
                                          {iitem.icon && (
                                            <Icon
                                              name={iitem.icon}
                                              className="size-5 shrink-0 text-blue-600 dark:text-blue-400"
                                            />
                                          )}
                                          <div>
                                            <div className="text-sm font-semibold text-slate-900 dark:text-slate-100">
                                              {iitem.title}
                                            </div>
                                            <p className="text-sm leading-snug text-slate-600 dark:text-slate-400">
                                              {iitem.description}
                                            </p>
                                          </div>
                                        </NavigationMenuLink>
                                      </li>
                                      
                                      {iitem.children.map((subItem, subIndex) => (
                                        <li key={`${ii}-${subIndex}`}>
                                          <NavigationMenuLink 
                                            href={subItem.url}
                                            className={cn(
                                              "flex select-none gap-4 rounded-lg p-3 leading-none no-underline outline-none transition-colors ml-4",
                                              "hover:bg-slate-100 dark:hover:bg-slate-700",
                                              "focus:bg-slate-100 dark:focus:bg-slate-700",
                                              "border-l-2 border-slate-200 dark:border-slate-600"
                                            )}
                                          >
                                            <Icon
                                              name="RiArrowRightSLine"
                                              className="size-4 shrink-0 text-slate-400"
                                            />
                                            <div>
                                              <div className="text-sm font-medium text-slate-800 dark:text-slate-200">
                                                {subItem.title}
                                              </div>
                                              <p className="text-xs leading-snug text-slate-500 dark:text-slate-500">
                                                {subItem.description}
                                              </p>
                                            </div>
                                          </NavigationMenuLink>
                                        </li>
                                      ))}
                                    </React.Fragment>
                                  );
                                }
                                
                                return (
                                  <li key={ii}>
                                    <NavigationMenuLink 
                                      href={iitem.url}
                                      className={cn(
                                        "flex select-none gap-4 rounded-lg p-3 leading-none no-underline outline-none transition-colors",
                                        "hover:bg-slate-100 dark:hover:bg-slate-700",
                                        "focus:bg-slate-100 dark:focus:bg-slate-700"
                                      )}
                                    >
                                      {iitem.icon && (
                                        <Icon
                                          name={iitem.icon}
                                          className="size-5 shrink-0 text-blue-600 dark:text-blue-400"
                                        />
                                      )}
                                      <div>
                                        <div className="text-sm font-semibold text-slate-900 dark:text-slate-100">
                                          {iitem.title}
                                        </div>
                                        <p className="text-sm leading-snug text-slate-600 dark:text-slate-400">
                                          {iitem.description}
                                        </p>
                                      </div>
                                    </NavigationMenuLink>
                                  </li>
                                );
                              })}
                            </ul>
                          </NavigationMenuContent>
                        </NavigationMenuItem>
                      );
                    }

                    return (
                      <NavigationMenuItem key={i}>
                        <a
                          className={cn(
                            "text-muted-foreground",
                            navigationMenuTriggerStyle,
                            buttonVariants({
                              variant: "ghost",
                            })
                          )}
                          href={item.url}
                          target={item.target}
                        >
                          {item.icon && (
                            <Icon
                              name={item.icon}
                              className="size-4 shrink-0 mr-0"
                            />
                          )}
                          {item.title}
                        </a>
                      </NavigationMenuItem>
                    );
                  })}
                </NavigationMenuList>
              </NavigationMenu>
            </div>
          </div>
          <div className="shrink-0 flex gap-2 items-center">
            {header.show_locale && <LocaleToggle />}
            {header.show_theme && <ThemeToggle />}

            {header.buttons?.map((item, i) => {
              return (
                <Button key={i} variant={item.variant}>
                  <Link
                    href={item.url || ""}
                    target={item.target || ""}
                    className="flex items-center gap-1"
                  >
                    {item.title}
                    {item.icon && (
                      <Icon name={item.icon} className="size-4 shrink-0" />
                    )}
                  </Link>
                </Button>
              );
            })}
            {header.show_sign && <SignToggle />}
          </div>
        </nav>

        <div className="block lg:hidden">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <a
                href={header.brand?.url || ""}
                className="flex items-center gap-2"
              >
                {header.brand?.logo?.src && (
                  <img
                    src={header.brand.logo.src}
                    alt={header.brand.logo.alt || header.brand.title}
                    className="w-8"
                  />
                )}
                {header.brand?.title && (
                  <span className="text-xl font-bold">
                    {header.brand?.title || ""}
                  </span>
                )}
              </a>
            </div>
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="default" size="icon">
                  <Menu className="size-2" />
                </Button>
              </SheetTrigger>
              <SheetContent className="overflow-y-auto">
                <SheetHeader>
                  <SheetTitle>
                    <div className="flex items-center gap-2">
                      {header.brand?.logo?.src && (
                        <img
                          src={header.brand.logo.src}
                          alt={header.brand.logo.alt || header.brand.title}
                          className="w-8"
                        />
                      )}
                      {header.brand?.title && (
                        <span className="text-xl font-bold">
                          {header.brand?.title || ""}
                        </span>
                      )}
                    </div>
                  </SheetTitle>
                </SheetHeader>
                <div className="mb-8 mt-8 flex flex-col gap-4">
                  <Accordion type="single" collapsible className="w-full">
                    {header.nav?.items?.map((item, i) => {
                      if (item.children && item.children.length > 0) {
                        return (
                          <AccordionItem
                            key={i}
                            value={item.title || ""}
                            className="border-b-0"
                          >
                            <AccordionTrigger className="mb-4 py-0 font-semibold hover:no-underline text-left">
                              {item.title}
                            </AccordionTrigger>
                            <AccordionContent className="mt-2">
                              {item.children.map((iitem, ii) => (
                                <Link
                                  key={ii}
                                  href={iitem.url || ""}
                                  target={iitem.target}
                                  className={cn(
                                    "flex select-none gap-4 rounded-md p-3 leading-none outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                  )}
                                >
                                  {iitem.icon && (
                                    <Icon
                                      name={iitem.icon}
                                      className="size-4 shrink-0"
                                    />
                                  )}
                                  <div>
                                    <div className="text-sm font-semibold">
                                      {iitem.title}
                                    </div>
                                    <p className="text-sm leading-snug text-muted-foreground">
                                      {iitem.description}
                                    </p>
                                  </div>
                                </Link>
                              ))}
                            </AccordionContent>
                          </AccordionItem>
                        );
                      }
                      return (
                        <Link
                          key={i}
                          href={item.url || ""}
                          target={item.target}
                          className="font-semibold my-4 flex items-center gap-2"
                        >
                          {item.title}
                          {item.icon && (
                            <Icon
                              name={item.icon}
                              className="size-4 shrink-0"
                            />
                          )}
                        </Link>
                      );
                    })}
                  </Accordion>
                </div>
                <div className="flex-1"></div>
                <div className="border-t pt-4">
                  <div className="mt-2 flex flex-col gap-3">
                    {header.buttons?.map((item, i) => {
                      return (
                        <Button key={i} variant={item.variant}>
                          <Link
                            href={item.url || ""}
                            target={item.target || ""}
                            className="flex items-center gap-1"
                          >
                            {item.title}
                            {item.icon && (
                              <Icon
                                name={item.icon}
                                className="size-4 shrink-0"
                              />
                            )}
                          </Link>
                        </Button>
                      );
                    })}

                    {header.show_sign && <SignToggle />}
                  </div>

                  <div className="mt-4 flex items-center gap-2">
                    {header.show_locale && <LocaleToggle />}
                    <div className="flex-1"></div>

                    {header.show_theme && <ThemeToggle />}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </section>
  );
}
