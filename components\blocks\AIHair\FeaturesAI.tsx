import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import Icon from '@/components/icon';

interface Feature {
  title: string;
  description: string;
  icon?: string;
  image?: string;
  url?: string;
}

interface FeaturesAIProps {
  section: {
    title: string;
    subtitle?: string;
    description?: string;
    features?: Feature[];
    items?: Feature[];
  };
}

export default function FeaturesAI({ section }: FeaturesAIProps) {
  const features = section.features || section.items || [];
  
  if (features.length === 0) {
    return (
      <div className="text-center py-16">
        <p className="text-slate-600">No features available</p>
      </div>
    );
  }
  
  return (
    <section className="py-20 bg-gradient-to-b from-slate-50 to-slate-100">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900">
            {section.title}
          </h2>
          
          {section.subtitle && (
            <p className="text-xl text-slate-700 mb-4">
              {section.subtitle}
            </p>
          )}
          
          {section.description && (
            <p className="text-slate-600">
              {section.description}
            </p>
          )}
        </div>
        
        <div className="grid md:grid-cols-2 gap-12 items-center">
          {/* 左侧：特性图片 */}
          <div className="relative h-[500px] rounded-2xl overflow-hidden shadow-2xl">
            {features[0]?.image ? (
              <Image
                src={features[0].image}
                alt={features[0].title}
                fill
                className="object-cover"
              />
            ) : (
              <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-indigo-700" />
            )}
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-8">
              <h3 className="text-2xl font-bold text-white mb-2">{features[0]?.title}</h3>
              <p className="text-slate-200">{features[0]?.description}</p>
            </div>
          </div>
          
          {/* 右侧：特性列表 */}
          <div className="space-y-6">
            {features.map((feature, index) => (
              <Card key={index} className="bg-white hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                      {feature.icon ? (
                        <Icon
                          name={feature.icon}
                          className="h-6 w-6 text-blue-600"
                        />
                      ) : (
                        <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      )}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-slate-900">{feature.title}</h3>
                      <p className="text-slate-600">{feature.description}</p>
                      {feature.url && (
                        <a 
                          href={feature.url}
                          className="inline-flex items-center mt-2 text-blue-600 hover:text-blue-700 font-medium"
                        >
                          Learn More
                          <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </a>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}