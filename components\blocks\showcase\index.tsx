//改成完全服务端渲染，SEO 友好，图片和描述会直接输出到 HTML

import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Section as SectionType } from "@/types/blocks/section";
import Image from 'next/image';

export default function Showcase({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-16">
      <div className="container">
        <div className="mb-8 flex items-end justify-between md:mb-14 lg:mb-16">
          <h2 className="mb-2 text-pretty text-3xl font-bold lg:text-4xl">
            {section.title}
          </h2>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {section.items?.map((item, i) => {
            // 安全获取图片源
            const imageSrc = typeof item.image === 'string' ? item.image : item.image?.src;
            
            if (!imageSrc) {
              return null; // 跳过没有图片的项目
            }
            
            // 安全获取alt文本
            const altText = typeof item.image === 'string' 
              ? (item.title || 'Hairstyle image')
              : (item.image?.alt || item.title || 'Hairstyle image');
            
            return (
              <div
                key={i}
                className="group flex flex-col justify-between rounded-xl border border-border bg-card p-6"
              >
                <div>
                  <div className="flex aspect-[3/2] overflow-clip rounded-xl">
                    <div className="flex-1">
                      <div className="relative h-full w-full origin-bottom transition duration-300 group-hover:scale-105">
                        <Image
                          src={imageSrc}
                          alt={altText}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          priority={false}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                {item.label && (
                  <div className="mt-6">
                    <Badge>{item.label}</Badge>
                  </div>
                )}
                <div className="mb-2 line-clamp-3 break-words pt-4 text-lg font-medium md:mb-3 md:pt-4 md:text-xl lg:pt-4 lg:text-2xl">
                  {item.title}
                </div>
                <div className="mb-2 line-clamp-5 text-sm text-muted-foreground md:mb-2 md:text-base lg:mb-2">
                  {item.description}
                </div>
                
                {/* 最小添加：标签显示 */}
                {item.tags && item.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1.5 mb-3">
                    {item.tags.map((tag: string, index: number) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}
                
                {/* 最小添加：按钮 */}
                {item.url && (
                  <Link href={item.url}>
                    <Button className="w-full mt-2 bg-blue-600 hover:bg-blue-700 text-white">
                      {section.viewStyleText || "View Style"}
                    </Button>
                  </Link>
                )}
              </div>
            );
          })}
        </div>
        
        {/* 最小添加：底部链接 */}
        {section.viewAllLink && (
          <div className="text-center mt-8">
            <Link href={section.viewAllLink}>
              <Button variant="outline" className="border-blue-500 text-blue-600 hover:bg-blue-50">
                {section.viewAllText || "Browse All"}
              </Button>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
}
