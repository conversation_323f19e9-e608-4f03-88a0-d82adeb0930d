'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface Stylist {
  id: string;
  name: string;
  title: string;
  bio: string;
  image: string;
  location: string;
  specialties: string[];
  socialLinks?: {
    instagram?: string;
    tiktok?: string;
    youtube?: string;
  };
  url?: string;
}

interface StylistShowcaseProps {
  section: {
    title: string;
    subtitle?: string;
    description?: string;
    stylists: Stylist[];
    viewAllLink?: string;
  };
}

export default function StylistShowcase({ section }: StylistShowcaseProps) {
  const [hoveredStylist, setHoveredStylist] = useState<string | null>(null);
  
  return (
    <div className="py-20 bg-gradient-to-b from-slate-900 to-slate-800 text-white">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            {section.title}
          </h2>
          
          {section.subtitle && (
            <p className="text-xl text-slate-300 mb-4">
              {section.subtitle}
            </p>
          )}
          
          {section.description && (
            <p className="text-slate-400">
              {section.description}
            </p>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {section.stylists.map((stylist) => (
            <div key={stylist.id}>
              <Card className="h-full bg-slate-800 border-slate-700">
                <div className="relative h-64 overflow-hidden">
                  <img
                    src={stylist.image}
                    alt={`${stylist.name} - Professional Hair Stylist`}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-4">
                    <h3 className="text-xl font-bold text-white">{stylist.name}</h3>
                    <p className="text-slate-300">{stylist.title}</p>
                    <div className="flex items-center mt-2">
                      <Badge variant="secondary" className="bg-slate-700 text-slate-200">
                        {stylist.location}
                      </Badge>
                    </div>
                  </div>
                </div>
                
                <CardContent className="p-6">
                  <p className="text-slate-300 mb-4">{stylist.bio}</p>
                  
                  <div className="mb-4">
                    <h4 className="font-semibold text-white mb-2">Specialties:</h4>
                    <div className="flex flex-wrap gap-2">
                      {stylist.specialties.map((specialty, index) => (
                        <Badge key={index} className="bg-blue-900 text-blue-100">
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
        
        {section.viewAllLink && (
          <div className="text-center mt-12">
            <Link href={section.viewAllLink}>
              <Button variant="outline" size="lg" className="border-2 border-white/20 text-white hover:bg-white/10">
                View All Stylists
              </Button>
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
