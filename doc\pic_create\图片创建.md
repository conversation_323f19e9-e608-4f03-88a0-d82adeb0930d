
### 建议方案：混合策略（推荐）

考虑到您的项目是Next.js应用，并且有大量的子页面，我强烈建议采用**混合策略**。这种策略既能保持通用组件图片的共享性，又能为特定内容提供清晰的结构。

**核心原则**:

*   **通用、可复用的图片放在共享目录。**
*   **特定于某个页面或某个发型（尤其是详情页）的图片放在独立目录。**
*   **目录结构尽可能反映URL结构或逻辑分类。**

#### 具体实施建议

1.  **通用图片目录 (`/public/imgs/general/` 或直接在 `/public/imgs/` 下分类)**:
    *   **用途**: 存放全站通用的图片，例如Logo、图标、SEO元图、通用的背景图、脸型示例图等。
    *   **示例路径**:
        *   `/public/imgs/logos/logo.svg`
        *   `/public/imgs/icons/camera.svg`
        *   `/public/imgs/face-shapes/face-oval.webp`
        *   `/public/imgs/cta-background.webp` (如果CTA背景图是通用的)

2.  **门户页面的分类/趋势图片目录 (`/public/imgs/categories/`, `/public/imgs/trending/`)**:
    *   **用途**: 存放像 `/men/` 这样的门户页面中，用于展示不同发型类别或趋势的缩略图/代表图。这些图片数量相对固定且较少。
    *   **示例路径**:
        *   `/public/imgs/categories/ultra-short.webp` (用于 `/men/` 页面的超短发类别)
        *   `/public/imgs/trending/textured-crop.webp` (用于 `/men/` 页面的趋势发型)

3.  **特定发型详情页图片目录 (`/public/imgs/hairstyles/[category]/[style-name]/`)**:
    *   **用途**: 存放每个具体发型详情页面（例如 `/men/buzz-cut/classic-fade`）所需的高清大图、多角度视图等。这是您68个子页面的主要图片来源。
    *   **优点**:
        *   文件名可以保持简洁（例如 `main.webp`, `side-view.webp`），因为父目录已经提供了足够的上下文。
        *   即使有数百个发型，每个发型的图片都单独存放，非常清晰。
        *   方便管理每个发型的图片版本和更新。
    *   **示例路径**:
        *   `/public/imgs/hairstyles/buzz-cut/classic-fade/main.webp`
        *   `/public/imgs/hairstyles/taper-fade/low-taper/angle-view.webp`
        *   `/public/imgs/hairstyles/korean-styles/two-block/details.webp`

#### 关于“同一个组件”的问题

*   **组件引用图片的方式主要通过路径，而不是物理位置。** 只要图片在 `public` 目录下，并且路径正确，任何组件都可以引用它。
*   **`PopularHairStyles` 和 `TrendingStyles` 组件**:
    *   在 `/men/page.tsx` 中，您现在是这样传入图片路径的：
        ```typescript
        image: `/imgs/categories/${item.id}.webp` // 或 `/imgs/styles/${style.id}.webp`
        ```
    *   这种方式非常好，它意味着 `HairstyleCategories` 和 `PopularHairStyles` 组件本身是通用的，它们接收一个 `item` 或 `style` 对象，然后根据 `id` 动态构建图片路径。
    *   因此，`public/imgs/styles/` 或 `public/imgs/categories/` 文件夹内的图片是为了适配这些通用组件的数据结构而存在的。

### 总结

对于您有68个子页面的情况，我建议：

*   **保留并继续使用 `/public/imgs/categories/` 和 `/public/imgs/trending/` 目录**：用于门户页面（如 `/men/`）中的分类和趋势展示，因为这些是少量、有代表性的图片。
*   **为每个具体发型详情页创建更深层次的目录结构**：例如 `public/imgs/hairstyles/[category]/[style-name]/`，用于存放特定发型的所有图片。这将极大地改善长期的可维护性。

这样，您的图片资源既能保持通用性和复用性，又能拥有清晰的、可扩展的结构，便于未来的管理和SEO优化。

----------------------------


-------------
Read file: doc/网站架构规划v3.md
Read file: doc/网站架构规划v3.md
Search files...
基于您的网站架构规划文档，我来为您梳理一个完整的全站图片目录规范混合方案：

## 🗂️ 全站图片目录规范 - 混合方案

### 📋 核心设计原则

1. **页面隔离原则**：每个主要页面类型有独立的图片目录
2. **功能共享原则**：通用组件和工具图片集中管理
3. **SEO友好原则**：目录结构与URL结构保持一致
4. **可扩展原则**：支持68个页面的长期扩展

### 🏗️ 完整目录结构

```
public/imgs/
├── 🌐 shared/                    # 全站共享资源
│   ├── logos/                    # 品牌Logo
│   │   ├── logo.svg
│   │   ├── logo-dark.svg
│   │   └── favicon.webp
│   ├── icons/                    # 通用图标
│   │   ├── camera.svg
│   │   ├── location.svg
│   │   ├── face.svg
│   │   └── ai.svg
│   ├── face-shapes/              # 脸型分析通用图
│   │   ├── face-oval.webp
│   │   ├── face-round.webp
│   │   ├── face-square.webp
│   │   ├── face-heart.webp
│   │   └── face-oblong.webp
│   ├── backgrounds/              # 通用背景图
│   │   ├── cta-background.webp
│   │   ├── gradient-blue.webp
│   │   └── pattern-subtle.webp
│   └── ui/                       # UI组件图片
│       ├── loading-spinner.gif
│       └── placeholder.webp
│
├── 🏠 landing/                   # 首页专用
│   ├── hero/
│   │   ├── landing-hero-bg.webp
│   │   └── hero-buzz-cut-showcase.webp
│   ├── popular/                  # 首页热门发型
│   │   ├── buzz-cut.webp
│   │   ├── caesar-cut.webp
│   │   ├── taper-fade.webp
│   │   ├── mullet.webp
│   │   ├── pixie.webp
│   │   └── bob.webp
│   ├── ai-tryion/                # AI试发功能展示
│   │   ├── before-haircut.webp
│   │   ├── after-haircut.webp
│   │   ├── buzz-style-1.webp
│   │   ├── buzz-style-2.webp
│   │   ├── caesar-style-1.webp
│   │   ├── fade-style-1.webp
│   │   ├── crew-style-1.webp
│   │   └── pixie-style-1.webp
│   ├── salons/                   # 理发店展示
│   │   ├── elite-cuts.webp
│   │   ├── modern-style.webp
│   │   └── precision-barbers.webp
│   └── tools/                    # 工具推荐
│       ├── clippers.webp
│       ├── trimmer.webp
│       ├── pomade.webp
│       └── scissors.webp
│
├── 👨‍🦰 men/                       # 男士发型专区
│   ├── hero/
│   │   └── mens-hairstyles-hero.webp
│   ├── categories/               # 男士分类导航图
│   │   ├── ultra-short.webp
│   │   ├── fade-styles.webp
│   │   ├── specialty.webp
│   │   ├── short-hair.webp
│   │   ├── medium-hair.webp
│   │   └── korean-styles.webp
│   ├── popular/                  # 男士热门发型
│   │   ├── buzz-cut.webp
│   │   ├── taper-fade.webp
│   │   ├── crew-cut.webp
│   │   ├── modern-mullet.webp
│   │   ├── undercut.webp
│   │   └── korean-cut.webp
│   ├── trending/                 # 男士趋势发型
│   │   ├── textured-crop.webp
│   │   ├── mid-fade.webp
│   │   ├── modern-buzz.webp
│   │   └── korean-style.webp
│   └── hairstyles/               # 详情页图片（68个子页面）
│       ├── ultra-short/
│       │   ├── buzz-cut/
│       │   │   ├── main.webp
│       │   │   ├── side-view.webp
│       │   │   ├── back-view.webp
│       │   │   └── variations/
│       │   │       ├── fade-variation.webp
│       │   │       ├── classic-variation.webp
│       │   │       └── military-variation.webp
│       │   ├── crew-cut/
│       │   ├── caesar-cut/
│       │   └── military/
│       ├── fade/
│       │   ├── taper-fade/
│       │   ├── low-fade/
│       │   ├── mid-fade/
│       │   ├── high-fade/
│       │   ├── skin-fade/
│       │   └── burst-fade/
│       ├── specialty/
│       │   ├── mullet/
│       │   ├── undercut/
│       │   ├── mohawk/
│       │   ├── quiff/
│       │   └── pompadour/
│       ├── short/
│       │   ├── korean/
│       │   └── professional/
│       └── medium/
│
├── 👩‍🦰 women/                     # 女士发型专区
│   ├── hero/
│   │   └── womens-hairstyles-hero.webp
│   ├── categories/               # 女士分类导航图
│   │   ├── pixie-cuts.webp
│   │   ├── bob-styles.webp
│   │   ├── long-hair.webp
│   │   ├── curly-styles.webp
│   │   └── straight-styles.webp
│   ├── popular/                  # 女士热门发型
│   │   ├── pixie-cut.webp
│   │   ├── bob-cut.webp
│   │   ├── lob-cut.webp
│   │   ├── beach-waves.webp
│   │   └── straight-long.webp
│   ├── trending/                 # 女士趋势发型
│   │   ├── curtain-bangs.webp
│   │   ├── wolf-cut.webp
│   │   ├── shag-cut.webp
│   │   └── butterfly-layers.webp
│   └── hairstyles/               # 详情页图片（25个子页面）
│       ├── pixie/
│       │   ├── classic/
│       │   └── variations/
│       ├── bob/
│       │   ├── classic/
│       │   ├── short/
│       │   └── long/
│       ├── braids/
│       │   ├── knotless/
│       │   ├── cornrows/
│       │   └── box-braids/
│       ├── special/
│       │   ├── wedding/
│       │   ├── mature/
│       │   └── party/
│       ├── ponytail/
│       ├── twist/
│       ├── locs/
│       ├── wolf-cut/
│       ├── butterfly/
│       └── layered/
│
├── 🔧 tools/                     # 工具产品中心
│   ├── hero/
│   │   └── tools-hero.webp
│   ├── categories/
│   │   ├── clippers.webp
│   │   └── styling-products.webp
│   └── products/
│       ├── professional-clippers/
│       ├── home-clippers/
│       ├── pomades/
│       ├── gels/
│       └── accessories/
│
├── 🎨 hair-color/                # 发色搭配系统
│   ├── hero/
│   │   └── color-hero.webp
│   ├── categories/
│   │   ├── natural-colors.webp
│   │   ├── bold-colors.webp
│   │   └── highlights.webp
│   └── examples/
│       ├── blonde-variations/
│       ├── brown-shades/
│       ├── red-tones/
│       └── creative-colors/
│
├── 📍 salon-finder/              # 美发店定位
│   ├── hero/
│   │   └── salon-finder-hero.webp
│   ├── featured-salons/
│   │   ├── urban-style.webp
│   │   ├── classic-cuts.webp
│   │   └── premium-salon.webp
│   └── salon-types/
│       ├── barbershop.webp
│       ├── unisex-salon.webp
│       └── specialty-salon.webp
│
├── 🤖 ai-try-on/                 # AI虚拟试发
│   ├── hero/
│   │   └── ai-hero.webp
│   ├── demo/
│   │   ├── demo-before.webp
│   │   ├── demo-after.webp
│   │   └── demo-process.gif
│   └── results/
│       ├── male-examples/
│       └── female-examples/
│
├── 📐 face-shape/                # 脸型匹配系统
│   ├── hero/
│   │   └── face-shape-hero.webp
│   ├── analysis/
│   │   ├── round-analysis.webp
│   │   ├── oval-analysis.webp
│   │   ├── square-analysis.webp
│   │   ├── heart-analysis.webp
│   │   └── oblong-analysis.webp
│   └── recommendations/
│       ├── round/
│       ├── oval/
│       ├── square/
│       ├── heart/
│       └── oblong/
│
├── 🌊 hair-texture/              # 发质匹配系统
│   ├── hero/
│   │   └── texture-hero.webp
│   ├── types/
│   │   ├── curly.webp
│   │   ├── wavy.webp
│   │   ├── thin.webp
│   │   └── thick.webp
│   └── care-guides/
│       ├── curly-care/
│       ├── wavy-care/
│       ├── thin-care/
│       └── thick-care/
│
├── 📈 trends/                    # 发型趋势
│   ├── hero/
│   │   └── trends-hero.webp
│   ├── 2024/
│   │   ├── spring-trends.webp
│   │   ├── summer-trends.webp
│   │   ├── fall-trends.webp
│   │   └── winter-trends.webp
│   └── seasonal/
│       ├── spring/
│       ├── summer/
│       ├── fall/
│       └── winter/
│
├── ⭐ celebrity/                 # 明星发型
│   ├── hero/
│   │   └── celebrity-hero.webp
│   ├── male-celebrities/
│   │   ├── hollywood-stars.webp
│   │   ├── sports-icons.webp
│   │   └── music-artists.webp
│   └── female-celebrities/
│       ├── red-carpet.webp
│       ├── everyday-style.webp
│       └── trend-setters.webp
│
└── 🕰️ vintage/                   # 复古风格
    ├── hero/
    │   └── vintage-hero.webp
    ├── eras/
    │   ├── 1920s.webp
    │   ├── 1950s.webp
    │   ├── 1970s.webp
    │   └── 1980s.webp
    └── modern-vintage/
        ├── retro-revival.webp
        └── contemporary-classic.webp
```

### 📊 图片规格标准化

#### 🖼️ 标准尺寸规范

| 用途 | 尺寸 | 比例 | 文件大小 | 格式 |
|------|------|------|----------|------|
| **Hero背景图** | 1920x1080px | 16:9 | <250KB | WebP |
| **分类导航图** | 400x300px | 4:3 | <100KB | WebP |
| **热门发型图** | 600x400px | 3:2 | <120KB | WebP |
| **趋势发型图** | 500x600px | 5:6 | <150KB | WebP |
| **详情页主图** | 800x600px | 4:3 | <200KB | WebP |
| **详情页侧视图** | 600x600px | 1:1 | <150KB | WebP |
| **工具产品图** | 400x400px | 1:1 | <80KB | WebP |
| **Logo图标** | 200x200px | 1:1 | <50KB | SVG/WebP |

### 🔗 路径映射关系

#### 页面组件与图片目录映射

```typescript
// 路径映射配置
const IMAGE_PATHS = {
  // 首页
  landing: {
    hero: '/imgs/landing/hero/',
    popular: '/imgs/landing/popular/',
    aiTryOn: '/imgs/landing/ai-tryion/',
    salons: '/imgs/landing/salons/',
    tools: '/imgs/landing/tools/'
  },
  
  // 男士页面
  men: {
    hero: '/imgs/men/hero/',
    categories: '/imgs/men/categories/',
    popular: '/imgs/men/popular/',
    trending: '/imgs/men/trending/',
    hairstyles: '/imgs/men/hairstyles/'
  },
  
  // 女士页面
  women: {
    hero: '/imgs/women/hero/',
    categories: '/imgs/women/categories/',
    popular: '/imgs/women/popular/',
    trending: '/imgs/women/trending/',
    hairstyles: '/imgs/women/hairstyles/'
  },
  
  // 功能页面
  tools: {
    hero: '/imgs/tools/hero/',
    categories: '/imgs/tools/categories/',
    products: '/imgs/tools/products/'
  },
  
  // 共享资源
  shared: {
    logos: '/imgs/shared/logos/',
    icons: '/imgs/shared/icons/',
    faceShapes: '/imgs/shared/face-shapes/',
    backgrounds: '/imgs/shared/backgrounds/'
  }
} as const;
```

### 📝 配置文件模板

#### 男士页面配置
```json:doc/pic_create/config_men_optimized.json
{
  "project_name": "Men's Hairstyles - Optimized Structure",
  "input_folder": "C:/Users/<USER>/Desktop/pic/men",
  "output_folder": "./public/imgs",
  "compression": {
    "target_size_kb": 100,
    "quality_start": 95,
    "quality_min": 10,
    "format": "webp"
  },
  "naming_rules": [
    {"folder": "men/hero", "filename": "mens-hairstyles-hero"},
    {"folder": "men/categories", "filename": "ultra-short"},
    {"folder": "men/categories", "filename": "fade-styles"},
    {"folder": "men/categories", "filename": "specialty"},
    {"folder": "men/categories", "filename": "short-hair"},
    {"folder": "men/categories", "filename": "medium-hair"},
    {"folder": "men/categories", "filename": "korean-styles"},
    {"folder": "men/popular", "filename": "buzz-cut"},
    {"folder": "men/popular", "filename": "taper-fade"},
    {"folder": "men/popular", "filename": "crew-cut"},
    {"folder": "men/popular", "filename": "modern-mullet"},
    {"folder": "men/popular", "filename": "undercut"},
    {"folder": "men/popular", "filename": "korean-cut"},
    {"folder": "men/trending", "filename": "textured-crop"},
    {"folder": "men/trending", "filename": "mid-fade"},
    {"folder": "men/trending", "filename": "modern-buzz"},
    {"folder": "men/trending", "filename": "korean-style"}
  ]
}
```

#### 女士页面配置
```json:doc/pic_create/config_women_optimized.json
{
  "project_name": "Women's Hairstyles - Optimized Structure",
  "input_folder": "C:/Users/<USER>/Desktop/pic/women",
  "output_folder": "./public/imgs",
  "compression": {
    "target_size_kb": 120,
    "quality_start": 90,
    "quality_min": 15,
    "format": "webp"
  },
  "naming_rules": [
    {"folder": "women/hero", "filename": "womens-hairstyles-hero"},
    {"folder": "women/categories", "filename": "pixie-cuts"},
    {"folder": "women/categories", "filename": "bob-styles"},
    {"folder": "women/categories", "filename": "long-hair"},
    {"folder": "women/categories", "filename": "curly-styles"},
    {"folder": "women/categories", "filename": "straight-styles"},
    {"folder": "women/popular", "filename": "pixie-cut"},
    {"folder": "women/popular", "filename": "bob-cut"},
    {"folder": "women/popular", "filename": "lob-cut"},
    {"folder": "women/popular", "filename": "beach-waves"},
    {"folder": "women/popular", "filename": "straight-long"},
    {"folder": "women/trending", "filename": "curtain-bangs"},
    {"folder": "women/trending", "filename": "wolf-cut"},
    {"folder": "women/trending", "filename": "shag-cut"},
    {"folder": "women/trending", "filename": "butterfly-layers"}
  ]
}
```

### 🚀 实施优势

1. **清晰隔离**：每个页面类型有独立目录，避免混乱
2. **SEO友好**：目录结构与URL结构一致
3. **可扩展性**：支持68个页面的图片管理
4. **性能优化**：按需加载，减少首页压力
5. **维护便利**：图片修改不影响其他页面
6. **CDN友好**：便于设置不同目录的缓存策略

### 📋 对应的页面代码修改

```typescript:app/[locale]/(default)/men/page.tsx
// 使用新的目录结构
const heroData = {
  // ...
  image: '/imgs/men/hero/mens-hairstyles-hero.webp'
};

const categoriesData = {
  // ...
  categories: page.categories.items.map(item => ({
    ...item,
    image: `/imgs/men/categories/${item.id}.webp`
  }))
};

const popularStylesData = page.popular.styles.map(style => ({
  ...style,
  image: `/imgs/men/popular/${style.id}.webp`
}));
```

这个混合方案既解决了您担心的目录混乱问题，又为68个页面提供了清晰的可扩展结构。每个主要功能都有独立的图片目录，便于管理和维护。


首页

我来为你的BuzzcutAI.hair网站首页制定完整的图片资源列表。

## 首页图片资源需求清单

### 1. Hero区域图片

#### 背景图片
**文件路径**: `/public/imgs/landing-hero-bg.jpg`
- **尺寸**: 1920x1080px
- **格式**: WebP (主要) + JPEG (备用)
- **文件大小**: <200KB
- **优先级**: 🔴 MVP必需
- **内容建议**: 
  - 现代理发店内景，专业灯光
  - 突出理发椅和专业工具
  - 暖色调，营造专业氛围
  - 避免人物面部特写，保持通用性

#### 英雄展示图
**文件路径**: `/public/imgs/hero-buzz-cut-showcase.jpg`
- **尺寸**: 800x600px
- **格式**: WebP + JPEG
- **文件大小**: <150KB
- **优先级**: 🔴 MVP必需
- **内容建议**:
  - 3-4种不同buzz cut风格的拼接展示
  - 包含fade、crew cut、military buzz等
  - 模特应为不同脸型的男性
  - 专业摄影质量，统一光线

### 2. 热门发型展示区域

#### 热门发型展示图 (6张)
**文件路径**: `/public/imgs/styles/`
- **尺寸**: 600x400px (横向比例 3:2)
- **格式**: WebP + JPEG
- **文件大小**: 每张<120KB
- **优先级**: 🔴 MVP必需

**具体图片清单** (与en.json对应):
1. `buzz-cut.jpg` - 经典buzz cut，干净的男性化外观，低维护
2. `caesar-cut.jpg` - 凯撒切，短发水平直刘海，经典风格
3. `taper-fade.jpg` - 渐变fade，从长到短的干净渐变，专业风格
4. `mullet.jpg` - 现代mullet，复古与现代结合，前短后长
5. `pixie.jpg` - 精灵短发，大胆的女性短发风格，优雅低维护
6. `bob.jpg` - 经典波波头，永恒多变的发型，中等长度

**内容要求**:
- 模特年龄25-40岁，不同族裔和性别
- 专业理发后的效果，清晰展示发型轮廓
- 横向构图，突出发型侧面和正面效果
- 45度角或侧面拍摄，展示发型层次
- 统一的专业摄影光线和背景
- 每张图片应体现对应发型的特点和风格标签

**发型特点对应**:
- **Buzz Cut**: 体现"Ultra-Short"、"Low Maintenance"特点
- **Caesar Cut**: 体现"Short"、"Classic"特点  
- **Taper Fade**: 体现"Fade"、"Professional"特点
- **Modern Mullet**: 体现"Specialty"、"Trending"特点
- **Pixie Cut**: 体现"Short"、"Elegant"特点
- **Classic Bob**: 体现"Medium"、"Versatile"特点

### 3. 脸型匹配器区域

#### 脸型示例图 (6张)
**文件路径**: `/public/imgs/face-shapes/`
- **尺寸**: 300x300px (正方形)
- **格式**: WebP + JPEG
- **文件大小**: 每张<80KB
- **优先级**: 🟡 重要但可延后

**具体图片清单** (与en.json对应):
1. `face-oval.jpg` - 椭圆脸型男性
2. `face-round.jpg` - 圆脸型男性
3. `face-square.jpg` - 方脸型男性
4. `face-diamond.jpg` - 钻石脸型男性
5. `face-heart.jpg` - 心形脸型男性
6. `face-oblong.jpg` - 长脸型男性

**内容要求**:
- 正面拍摄，面部特征清晰
- 简洁背景，突出脸型轮廓
- 表情自然，无明显发型干扰
- 统一光线和色调

### 4. AI虚拟试发区域

#### AI演示图片
**文件路径**: `/public/imgs/ai-demo/`
- **尺寸**: 500x600px (竖向比例)
- **格式**: WebP + JPEG
- **优先级**: 🔴 MVP必需

**具体图片清单** (与en.json对应):
1. `before-haircut.jpg` - AI处理前的原始照片
2. `after-haircut.jpg` - AI生成的buzz cut效果图

**AI试发发型图片** (6张):
**文件路径**: `/public/imgs/styles/`
- **尺寸**: 300x360px (竖向比例 5:6)
- **格式**: WebP + JPEG
- **优先级**: 🔴 MVP必需

1. `buzz-style-1.jpg` - 经典buzz cut样式
2. `buzz-style-2.jpg` - buzz cut fade样式
3. `caesar-style-1.jpg` - 凯撒切样式
4. `fade-style-1.jpg` - 渐变fade样式
5. `crew-style-1.jpg` - 板寸头样式
6. `pixie-style-1.jpg` - 精灵短发样式

### 5. 理发店展示区域

#### 理发店图片 (3张)
**文件路径**: `/public/imgs/salons/`
- **尺寸**: 400x300px (横向比例 4:3)
- **格式**: WebP + JPEG
- **优先级**: 🟡 重要但可延后

**具体图片清单** (与en.json对应):
1. `elite-cuts.jpg` - Elite Cuts理发店，专业环境
2. `modern-style.jpg` - Modern Style工作室，现代设计
3. `precision-barbers.jpg` - Precision理发店，精准专业

### 6. 工具推荐区域

#### 理发工具产品图 (4张)
**文件路径**: `/public/imgs/tools/`
- **尺寸**: 400x400px (正方形)
- **格式**: WebP + JPEG
- **优先级**: 🟢 可选优化

**具体图片清单** (与en.json对应):
1. `clippers.jpg` - 专业电推剪，高质量理发工具
2. `trimmer.jpg` - 精密修剪器，细节修剪工具
3. `pomade.jpg` - 哑光发蜡，中等定型力
4. `scissors.jpg` - 专业理发剪，精密剪发工具

### 7. CTA区域

#### 行动号召背景图
**文件路径**: `/public/imgs/cta-background.jpg`
- **尺寸**: 1200x600px
- **格式**: WebP + JPEG
- **优先级**: 🟡 重要但可延后
- **内容建议**: 
  - 现代理发店环境
  - 专业设备展示
  - 或者抽象几何背景

## 🎨 图片生成Midjourney提示词

### 热门发型展示图提示词

#### 1. Buzz Cut (`buzz-cut.jpg`)

## 获取图片的推荐方式

### 🎯 优先推荐 (MVP阶段)

#### 1. AI生成工具
- **Midjourney**: 高质量发型图片生成
  ```
  提示词示例: "professional buzz cut hairstyle, modern barbershop, clean fade, 4K photography, studio lighting --ar 4:5"
  ```
- **DALL-E 3**: 真实感人像生成
- **Stable Diffusion**: 开源免费选项

#### 2. 专业图库 (预算充足)
- **Shutterstock**: 搜索 "buzz cut men", "male haircuts", "barbershop"
- **Getty Images**: 高质量专业摄影
- **Adobe Stock**: 与AI工具结合使用

#### 3. 免费资源 (预算有限)
- **Unsplash**: 搜索 "men haircut", "barber", "male portrait"
- **Pexels**: 质量不错的免费照片
- **Pixabay**: 基础图片资源

### 🔧 图片处理建议

#### 批量优化脚本
```bash
# WebP转换和压缩
for img in *.jpg; do
  # 生成WebP版本
  cwebp -q 85 "$img" -o "${img%.jpg}.webp"
  
  # 压缩原始JPEG
  jpegoptim --max=85 --strip-all "$img"
done
```

#### 尺寸标准化
```bash
# 使用ImageMagick批量调整尺寸
mogrify -resize 400x500^ -gravity center -extent 400x500 hairstyles/*.jpg
mogrify -resize 300x300^ -gravity center -extent 300x300 face-shapes/*.jpg
```

## 优先级实施计划

### 🔴 第一阶段 (MVP必需)
1. Hero背景图 - `landing-hero-bg.jpg`
2. 核心发型展示图 - 4张主要buzz cut样式
3. AI演示图 - 前后对比各1张

**预计成本**: $50-100 (使用AI生成 + 部分付费图库)

### 🟡 第二阶段 (功能完善)
1. 完整发型展示集 - 6张发型图
2. 脸型分析图 - 6张脸型示例
3. CTA背景图

**预计成本**: $100-200

### 🟢 第三阶段 (体验优化)
1. 工具产品图
2. 更多AI演示案例
3. 装饰性图标和插图

**预计成本**: $50-100

## SEO优化的图片命名建议

```
✅ 好的命名:
- buzz-cut-fade-men-2024.jpg
- classic-buzz-cut-hairstyle.jpg
- ai-haircut-generator-demo.jpg
- mens-short-haircut-styles.jpg

❌ 避免的命名:
- IMG_1234.jpg
- photo1.png
- untitled.jpeg
```

### Alt文本示例
```html
alt="Modern buzz cut with fade for men - professional barbershop style"
alt="AI-powered virtual haircut transformation demo"
alt="Square face shape ideal for buzz cut hairstyles"
alt="Professional hair clippers for buzz cut styling"
```

这个计划可以让你有序地建设图片资源，优先保证核心功能的视觉效果，然后逐步完善用户体验。建议先从AI生成工具开始，快速获得MVP阶段需要的图片，再根据用户反馈优化。


/men/ultra-short/military/`
主要页面图片
/imgs/men/ultra-short/military/hero.webp - 主页面英雄图
/imgs/men/ultra-short/military/introduction.webp - 军事发型介绍图
/imgs/men/ultra-short/military/regulations-chart.webp - 军事规定图表
2. 军事发型样式图片
/imgs/men/ultra-short/military/classic-army-cut.webp - 经典军队理发
/imgs/men/ultra-short/military/fade-army-haircut.webp - 渐变军队理发
/imgs/men/ultra-short/military/high-and-tight.webp - 高紧式理发
/imgs/men/ultra-short/military/air-force-cut.webp - 空军理发
/imgs/men/ultra-short/military/navy-cut.webp - 海军理发
/imgs/men/ultra-short/military/special-forces-cut.webp - 特种部队理发
3. 军事灵感图片
/imgs/men/ultra-short/military/marine-high-tight.webp - 海军陆战队高紧式
/imgs/men/ultra-short/military/army-ranger-cut.webp - 陆军游骑兵理发
/imgs/men/ultra-short/military/air-force-regulation.webp - 空军规定理发
4. 工具图片
/imgs/men/ultra-short/military/military-grade-clippers.webp - 军用级理发器
/imgs/men/ultra-short/military/military-guard-set.webp - 军用护罩套装
/imgs/men/ultra-short/military/military-grooming-kit.webp - 军用美容套装
5. 目标受众图片
/imgs/men/ultra-short/military/active-military-personnel.webp - 现役军人
/imgs/men/ultra-short/military/veterans-and-reservists.webp - 退伍军人和预备役
/imgs/men/ultra-short/military/law-enforcement.webp - 执法人员
/imgs/men/ultra-short/military/corporate-professionals.webp - 企业专业人士