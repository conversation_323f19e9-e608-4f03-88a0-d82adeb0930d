import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface HairstyleShowcaseProps {
  section: {
    title: string;
    subtitle?: string;
    description?: string;
    styles: Array<{
      id: string;
      name: string;
      description: string;
      image: string;
      length: string;
      difficulty: "Easy" | "Medium" | "Hard";
      maintenance: "Low" | "Medium" | "High";
      features?: string[];
      suitableFor?: string[];
      tags?: string[];
    }>;
  };
}

const difficultyColors = {
  Easy: "bg-green-100 text-green-800",
  Medium: "bg-yellow-100 text-yellow-800", 
  Hard: "bg-red-100 text-red-800",
};

const maintenanceColors = {
  Low: "bg-blue-100 text-blue-800",
  Medium: "bg-purple-100 text-purple-800",
  High: "bg-orange-100 text-orange-800",
};

export default function HairstyleShowcase({ section }: HairstyleShowcaseProps) {
  return (
    <section className="py-16 px-4 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {section.title}
          </h2>
          {section.subtitle && (
            <p className="text-xl text-gray-600 mb-4">
              {section.subtitle}
            </p>
          )}
          {section.description && (
            <p className="text-lg text-gray-500 max-w-3xl mx-auto">
              {section.description}
            </p>
          )}
        </div>

        {/* Styles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {section.styles.map((style) => (
            <Card key={style.id} className="group hover:shadow-xl transition-all duration-300 overflow-hidden border-0 bg-white">
              {/* Image */}
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={style.image}
                  alt={style.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
              </div>

              <CardContent className="p-4">
                {/* Title and Description */}
                <CardHeader className="p-0 mb-3">
                  <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
                    {style.name}
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-600">
                    {style.description}
                  </CardDescription>
                </CardHeader>

                {/* Specifications */}
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Length:</span>
                    <span className="font-medium text-gray-900">{style.length}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500 text-sm">Difficulty:</span>
                    <Badge className={`text-xs ${difficultyColors[style.difficulty]}`}>
                      {style.difficulty}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500 text-sm">Maintenance:</span>
                    <Badge className={`text-xs ${maintenanceColors[style.maintenance]}`}>
                      {style.maintenance}
                    </Badge>
                  </div>
                </div>

                {/* Tags */}
                {style.tags && style.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-4">
                    {style.tags.slice(0, 3).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}

                {/* Action Button */}
                <Button className="w-full text-sm bg-gray-900 hover:bg-gray-800 transition-colors">
                  View Details
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View More Button */}
        <div className="text-center mt-12">
          <Button size="lg" variant="outline" className="px-8 py-3">
            View All Styles
          </Button>
        </div>
      </div>
    </section>
  );
} 