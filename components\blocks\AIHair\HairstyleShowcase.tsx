'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface HairstyleShowcaseProps {
  section: {
    title: string;
    subtitle?: string;
    description?: string;
    styles: Array<{
      id: string;
      name: string;
      description: string;
      image: string;
      length: string;
      difficulty: "Easy" | "Medium" | "Hard";
      maintenance: "Low" | "Medium" | "High";
      features?: string[];
      suitableFor?: string[];
      tags?: string[];
      url?: string;
    }>;
    viewAllLink?: string;
  };
}

const difficultyColors = {
  Easy: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  Medium: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300", 
  Hard: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

const maintenanceColors = {
  Low: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  Medium: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
  High: "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300",
};

export function HairstyleShowcase({ section }: HairstyleShowcaseProps) {
  const [hoveredStyle, setHoveredStyle] = useState<string | null>(null);
  
  return (
    <section className="py-20 bg-gradient-to-b from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white">
            {section.title}
          </h2>
          
          {section.subtitle && (
            <p className="text-xl text-slate-700 dark:text-slate-300 mb-4">
              {section.subtitle}
            </p>
          )}
          
          {section.description && (
            <p className="text-slate-600 dark:text-slate-400">
              {section.description}
            </p>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {section.styles.map((style) => (
            <Link 
              key={style.id} 
              href={style.url || '#'}
              className="block"
              onMouseEnter={() => setHoveredStyle(style.id)}
              onMouseLeave={() => setHoveredStyle(null)}
            >
              <Card className="h-full overflow-hidden transition-all duration-300 hover:shadow-xl border-2 hover:border-blue-500 dark:border-slate-700 dark:hover:border-blue-500">
                <div className="relative h-64 overflow-hidden">
                  <Image
                    src={style.image}
                    alt={style.name}
                    fill
                    className={`object-cover transition-transform duration-500 ${hoveredStyle === style.id ? 'scale-110' : 'scale-100'}`}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex flex-col justify-end p-4">
                    <h3 className="text-xl font-bold text-white">{style.name}</h3>
                    <div className="flex flex-wrap gap-2 mt-2">
                      <Badge className={difficultyColors[style.difficulty]}>
                        {style.difficulty}
                      </Badge>
                      <Badge className={maintenanceColors[style.maintenance]}>
                        {style.maintenance} Maintenance
                      </Badge>
                      <Badge variant="outline" className="bg-white/20 text-white border-white/40">
                        {style.length}
                      </Badge>
                    </div>
                  </div>
                </div>
                <CardContent className="p-6">
                  <p className="text-slate-600 dark:text-slate-300 mb-4">{style.description}</p>
                  
                  {style.features && style.features.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-semibold text-slate-900 dark:text-white mb-2">Features:</h4>
                      <ul className="list-disc list-inside text-slate-600 dark:text-slate-300">
                        {style.features.slice(0, 3).map((feature, index) => (
                          <li key={index}>{feature}</li>
                        ))}
                        {style.features.length > 3 && <li>More...</li>}
                      </ul>
                    </div>
                  )}
                  
                  {style.tags && style.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-4">
                      {style.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="bg-slate-100 text-slate-700 dark:bg-slate-700 dark:text-slate-300">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                  
                  <div className={`mt-6 transition-opacity duration-300 ${hoveredStyle === style.id ? 'opacity-100' : 'opacity-0'}`}>
                    <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
        
        {section.viewAllLink && (
          <div className="text-center mt-12">
            <Link href={section.viewAllLink}>
              <Button variant="outline" size="lg" className="border-2 border-blue-500 text-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:hover:bg-blue-950">
                View All Hairstyles
              </Button>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
}