import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { getImage } from "@/models/image"
import moment from "moment";

export default async function () {
  const aiimage = await getImage(1, 50);

  const columns: TableColumn[] = [
    { name: "uuid", title: "UUID" },
    { name: "img_description", title: "Description" },
    { 
      name: "img_url", 
      title: "URL",
      callback: (row) => (
        <a href={row.img_url} target="_blank" rel="noopener">
          <img src={row.img_url} alt="preview" className="w-40 h-20 rounded-md" />
        </a>
      )
    },
    {
      name: "status",
      title: "Status",
      callback: (row) => (
        <img src={row.status} alt={`Status: ${row.status}`} className="w-10 h-10 rounded-full" />
      ),
    },
    {
      name: "created_at",
      title: "Created At",
      callback: (row) => moment(row.created_at).format("YYYY-MM-DD HH:mm:ss"),
    },
  ];

  const table: TableSlotType = {
    title: "All Aiimage",
    columns,
    data: aiimage,
  };

  return <TableSlot {...table} />;
}
