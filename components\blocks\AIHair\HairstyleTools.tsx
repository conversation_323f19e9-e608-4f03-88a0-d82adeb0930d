'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { StarIcon } from '@heroicons/react/24/solid';

interface Product {
  id: string;
  name: string;
  description: string;
  image: string;
  price: string;
  rating: number;
  url: string;
}

interface HairstyleToolsProps {
  section: {
    title: string;
    subtitle?: string;
    description?: string;
    items: Product[];
  };
}

export default function HairstyleTools({ section }: HairstyleToolsProps) {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  
  return (
    <div>
      <div className="text-center max-w-3xl mx-auto mb-12">
        <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900">
          {section.title}
        </h2>
        
        {section.subtitle && (
          <p className="text-lg text-blue-600 font-medium mb-2">
            {section.subtitle}
          </p>
        )}
        
        {section.description && (
          <p className="text-slate-600">
            {section.description}
          </p>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {section.items.map((item) => (
          <div 
            key={item.id}
            onMouseEnter={() => setHoveredItem(item.id)}
            onMouseLeave={() => setHoveredItem(null)}
          >
            <Card className="h-full overflow-hidden hover:shadow-xl transition-shadow duration-300">
              <div className="relative h-48 overflow-hidden">
                <img
                  src={item.image}
                  alt={item.name}
                  className={`w-full h-full object-cover transition-transform duration-500 ${hoveredItem === item.id ? 'scale-110' : 'scale-100'}`}
                />
                <div className="absolute top-0 right-0 bg-blue-600 text-white px-3 py-1 text-sm font-bold">
                  {item.price}
                </div>
              </div>
              
              <CardContent className="p-6">
                <div className="flex items-center mb-2">
                  {[...Array(5)].map((_, i) => (
                    <StarIcon 
                      key={i} 
                      className={`h-4 w-4 ${i < Math.floor(item.rating) ? 'text-yellow-400' : 'text-gray-300'}`} 
                    />
                  ))}
                  <span className="ml-2 text-sm text-slate-600">{item.rating}</span>
                </div>
                
                <h3 className="text-lg font-bold text-slate-900 mb-2">{item.name}</h3>
                <p className="text-slate-700 mb-4">{item.description}</p>
                
                <a href={item.url} target="_blank" rel="noopener noreferrer">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                    View Details
                  </Button>
                </a>
              </CardContent>
            </Card>
          </div>
        ))}
      </div>
    </div>
  );
}
