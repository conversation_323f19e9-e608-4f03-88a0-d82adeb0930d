import pandas as pd
import re
import json
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict
import chardet

@dataclass
class KeywordClassification:
    """关键词分类结果数据类"""
    keyword: str
    search_volume: int
    competition: float
    primary_category: str
    demographics: str
    search_intent: str
    face_shape: str
    hair_texture: str
    occasion: str
    target_page: str
    priority: str
    confidence_score: float

class BuzzcutKeywordClassifier:
    """Buzzcut AI 关键词自动分类器"""
    
    def __init__(self):
        """初始化分类器和规则"""
        self.initialize_classification_rules()
        self.initialize_url_mapping()
        
    def initialize_classification_rules(self):
        """基于前1514行数据分析建立的分类规则"""
        
        # 1. 主分类规则 (Primary Category)
        self.primary_category_rules = {
            'Ultra_Short_Hair': {
                'keywords': ['buzz cut', 'buzzcut', 'buzz', 'crew cut', 'caesar cut', 'caesar', 
                           'military cut', 'army cut', 'military', 'skin fade', 'zero cut',
                           'brush cut', 'guard buzz', 'clipper buzz'],
                'patterns': [r'\bbuzz\b', r'\bcrew\b', r'\bcaesar\b', r'\bmilitary\b', 
                           r'\barmy\b', r'\bguard\b.*\bbuzz\b', r'\bbuzz\b.*\bcut\b']
            },
            'Fade_Techniques': {
                'keywords': ['taper fade', 'fade', 'low fade', 'mid fade', 'high fade', 
                           'skin fade', 'burst fade', 'drop fade', 'temp fade'],
                'patterns': [r'\bfade\b(?!\s+haircut)', r'\btaper\b.*\bfade\b', 
                           r'\b(low|mid|high|skin|burst|drop)\s+fade\b']
            },
            'Short_Hair_Women': {
                'keywords': ['pixie cut', 'pixie', 'bob haircut', 'bob cut', 'bob', 
                           'short hair', 'short haircuts', 'short hairstyles'],
                'patterns': [r'\bpixie\b', r'\bbob\b(?!\s+marley)', r'\bshort\b.*\bhair\b',
                           r'\bshort\b.*\b(cut|style)\b']
            },
            'Short_Hair_Men': {
                'keywords': ['short haircuts for men', 'mens short haircuts', 'short hair men',
                           'guys short haircuts', 'male short hairstyles'],
                'patterns': [r'\bshort\b.*\b(men|mens|male|guys)\b', 
                           r'\b(men|mens|male|guys)\b.*\bshort\b']
            },
            'Braided_Styles': {
                'keywords': ['knotless braids', 'goddess braids', 'boho braids', 'box braids',
                           'cornrows', 'braided hairstyles', 'braids', 'plaits'],
                'patterns': [r'\bbraids?\b', r'\bcornrows?\b', r'\bplaits?\b', 
                           r'\b(knotless|goddess|boho|box)\s+braids?\b']
            },
            'Medium_Length': {
                'keywords': ['shoulder length', 'medium length', 'layered haircuts', 
                           'medium hairstyles', 'lob haircut'],
                'patterns': [r'\bmedium\b.*\b(length|hair)\b', r'\bshoulder\b.*\blength\b',
                           r'\blayered\b.*\b(cut|style)\b', r'\blob\b.*\bhaircut\b']
            },
            'Special_Styles': {
                'keywords': ['undercut', 'mohawk', 'mullet', 'shag', 'wolf cut', 'quiff'],
                'patterns': [r'\bundercut\b', r'\bmohawk\b', r'\bmullet\b', 
                           r'\bshag\b', r'\bwolf\s+cut\b', r'\bquiff\b']
            },
            'Wedding_Event': {
                'keywords': ['wedding hairstyles', 'bridal hairstyles', 'bridesmaid hairstyles',
                           'party hairstyles', 'formal hairstyles', 'prom hairstyles'],
                'patterns': [r'\b(wedding|bridal|bridesmaid|party|formal|prom)\b.*\b(hair|style)\b']
            }
        }
        
        # 2. 人群分类规则 (Demographics)
        self.demographics_rules = {
            'Men': {
                'keywords': ['men', 'mens', 'male', 'guys', 'gentleman', 'masculine'],
                'patterns': [r'\b(men|mens|male|guys|gentleman|masculine)\b']
            },
            'Women': {
                'keywords': ['women', 'womens', 'female', 'ladies', 'girls', 'feminine'],
                'patterns': [r'\b(women|womens|female|ladies|girls|feminine)\b']
            },
            'Mature': {
                'keywords': ['over 60', 'over 50', 'older women', 'older ladies', 'mature',
                           'senior', 'elderly'],
                'patterns': [r'\bover\s+(50|60|70)\b', r'\b(older|mature|senior|elderly)\b']
            },
            'Teen': {
                'keywords': ['teenage', 'teen', 'young', 'student'],
                'patterns': [r'\b(teenage|teen|young)\b.*\b(guys|men|women|girls)\b']
            },
            'Universal': {
                'keywords': [],
                'patterns': []
            }
        }
        
        # 3. 搜索意图规则 (Search Intent)
        self.search_intent_rules = {
            'Local': {
                'keywords': ['near me', 'salon', 'barber', 'shop', 'nearby'],
                'patterns': [r'\bnear\s+me\b', r'\bsalon\b', r'\bbarber\b', 
                           r'\bnearby\b', r'\bshop\b']
            },
            'Commercial': {
                'keywords': ['clippers', 'tools', 'products', 'buy', 'price', 'cost',
                           'gel', 'wax', 'spray', 'equipment'],
                'patterns': [r'\b(clippers?|tools?|products?)\b', r'\b(gel|wax|spray)\b',
                           r'\b(buy|price|cost)\b', r'\btry\s+on\b']
            },
            'Informational': {
                'keywords': ['hairstyles', 'haircuts', 'how to', 'ideas', 'inspiration'],
                'patterns': [r'\b(hairstyles?|haircuts?)\b', r'\bhow\s+to\b', 
                           r'\b(ideas?|inspiration)\b']
            }
        }
        
        # 4. 脸型分类规则 (Face Shape)
        self.face_shape_rules = {
            'Round': {
                'keywords': ['round face', 'chubby face', 'fat face', 'full face'],
                'patterns': [r'\b(round|chubby|fat|full)\s+face\b']
            },
            'Oval': {
                'keywords': ['oval face', 'oblong face'],
                'patterns': [r'\b(oval|oblong)\s+face\b']
            },
            'Square': {
                'keywords': ['square face', 'angular face'],
                'patterns': [r'\b(square|angular)\s+face\b']
            },
            'All': {
                'keywords': [],
                'patterns': []
            }
        }
        
        # 5. 发质分类规则 (Hair Texture)
        self.hair_texture_rules = {
            'Curly': {
                'keywords': ['curly hair', 'wavy hair', 'natural hair'],
                'patterns': [r'\b(curly|wavy|natural)\s+hair\b']
            },
            'Straight': {
                'keywords': ['straight hair', 'fine hair'],
                'patterns': [r'\b(straight|fine)\s+hair\b']
            },
            'Thin': {
                'keywords': ['thin hair', 'fine hair', 'thinning hair'],
                'patterns': [r'\b(thin|fine|thinning)\s+hair\b']
            },
            'Thick': {
                'keywords': ['thick hair', 'coarse hair'],
                'patterns': [r'\b(thick|coarse)\s+hair\b']
            },
            'All': {
                'keywords': [],
                'patterns': []
            }
        }
    
    def initialize_url_mapping(self):
        """初始化URL映射规则"""
        self.url_mapping = {
            'Ultra_Short_Hair': {
                'Men': '/men/ultra-short/',
                'Women': '/women/short/',
                'Universal': '/hairstyles/ultra-short/'
            },
            'Fade_Techniques': {
                'Men': '/men/fade/',
                'Women': '/women/fade/',
                'Universal': '/techniques/fade/'
            },
            'Short_Hair_Women': {
                'Women': '/women/short/',
                'Universal': '/hairstyles/short/'
            },
            'Short_Hair_Men': {
                'Men': '/men/short/',
                'Universal': '/hairstyles/short/'
            },
            'Braided_Styles': {
                'Women': '/women/braids/',
                'Men': '/men/braids/',
                'Universal': '/hairstyles/braids/'
            },
            'Medium_Length': {
                'Women': '/women/medium/',
                'Men': '/men/medium/',
                'Universal': '/hairstyles/medium/'
            },
            'Special_Styles': {
                'Men': '/men/specialty/',
                'Women': '/women/specialty/',
                'Universal': '/hairstyles/specialty/'
            },
            'Wedding_Event': {
                'Women': '/women/wedding/',
                'Universal': '/occasion/wedding/'
            }
        }
    
    def classify_keyword(self, keyword: str, search_volume: int, competition: float) -> KeywordClassification:
        """对单个关键词进行分类"""
        keyword_lower = keyword.lower()
        
        # 分类各个维度
        primary_category, primary_confidence = self._classify_primary_category(keyword_lower)
        demographics, demo_confidence = self._classify_demographics(keyword_lower)
        search_intent, intent_confidence = self._classify_search_intent(keyword_lower)
        face_shape, face_confidence = self._classify_face_shape(keyword_lower)
        hair_texture, texture_confidence = self._classify_hair_texture(keyword_lower)
        occasion = self._classify_occasion(keyword_lower)
        
        # 计算综合置信度
        confidence_score = (primary_confidence + demo_confidence + intent_confidence) / 3
        
        # 生成目标页面URL
        target_page = self._generate_target_page(primary_category, demographics)
        
        # 计算优先级
        priority = self._calculate_priority(search_volume, competition, confidence_score)
        
        return KeywordClassification(
            keyword=keyword,
            search_volume=search_volume,
            competition=competition,
            primary_category=primary_category,
            demographics=demographics,
            search_intent=search_intent,
            face_shape=face_shape,
            hair_texture=hair_texture,
            occasion=occasion,
            target_page=target_page,
            priority=priority,
            confidence_score=confidence_score
        )
    
    def _classify_primary_category(self, keyword: str) -> Tuple[str, float]:
        """分类主类别"""
        max_score = 0
        best_category = 'Other'
        
        for category, rules in self.primary_category_rules.items():
            score = 0
            
            # 检查关键词匹配
            for kw in rules['keywords']:
                if kw in keyword:
                    score += 1
            
            # 检查正则匹配
            for pattern in rules['patterns']:
                if re.search(pattern, keyword, re.IGNORECASE):
                    score += 0.8
            
            if score > max_score:
                max_score = score
                best_category = category
        
        confidence = min(max_score / 2, 1.0)  # 归一化置信度
        return best_category, confidence
    
    def _classify_demographics(self, keyword: str) -> Tuple[str, float]:
        """分类目标人群"""
        max_score = 0
        best_demo = 'Universal'
        
        for demo, rules in self.demographics_rules.items():
            score = 0
            
            for kw in rules['keywords']:
                if kw in keyword:
                    score += 1
            
            for pattern in rules['patterns']:
                if re.search(pattern, keyword, re.IGNORECASE):
                    score += 1
            
            if score > max_score:
                max_score = score
                best_demo = demo
        
        confidence = min(max_score, 1.0)
        return best_demo, confidence
    
    def _classify_search_intent(self, keyword: str) -> Tuple[str, float]:
        """分类搜索意图"""
        max_score = 0
        best_intent = 'Informational'
        
        for intent, rules in self.search_intent_rules.items():
            score = 0
            
            for kw in rules['keywords']:
                if kw in keyword:
                    score += 1
            
            for pattern in rules['patterns']:
                if re.search(pattern, keyword, re.IGNORECASE):
                    score += 1
            
            if score > max_score:
                max_score = score
                best_intent = intent
        
        confidence = min(max_score, 1.0)
        return best_intent, confidence
    
    def _classify_face_shape(self, keyword: str) -> Tuple[str, float]:
        """分类脸型"""
        for shape, rules in self.face_shape_rules.items():
            for pattern in rules['patterns']:
                if re.search(pattern, keyword, re.IGNORECASE):
                    return shape, 1.0
        return 'All', 0.5
    
    def _classify_hair_texture(self, keyword: str) -> Tuple[str, float]:
        """分类发质"""
        for texture, rules in self.hair_texture_rules.items():
            for pattern in rules['patterns']:
                if re.search(pattern, keyword, re.IGNORECASE):
                    return texture, 1.0
        return 'All', 0.5
    
    def _classify_occasion(self, keyword: str) -> str:
        """分类场合"""
        if re.search(r'\b(wedding|bridal|bride)\b', keyword, re.IGNORECASE):
            return 'Wedding'
        elif re.search(r'\b(party|formal|prom)\b', keyword, re.IGNORECASE):
            return 'Party'
        elif re.search(r'\b(professional|business|work)\b', keyword, re.IGNORECASE):
            return 'Professional'
        else:
            return 'Casual'
    
    def _generate_target_page(self, primary_category: str, demographics: str) -> str:
        """生成目标页面URL"""
        if primary_category in self.url_mapping:
            if demographics in self.url_mapping[primary_category]:
                return self.url_mapping[primary_category][demographics]
            else:
                return self.url_mapping[primary_category].get('Universal', '/hairstyles/')
        return '/hairstyles/'
    
    def _calculate_priority(self, search_volume: int, competition: float, confidence: float) -> str:
        """计算关键词优先级"""
        # 优先级计算逻辑
        if search_volume >= 500000 and competition <= 10 and confidence >= 0.7:
            return 'Critical'
        elif search_volume >= 50000 and competition <= 30 and confidence >= 0.6:
            return 'High'
        elif search_volume >= 5000 and competition <= 50 and confidence >= 0.5:
            return 'Medium'
        else:
            return 'Low'

def detect_file_encoding(file_path: str) -> str:
    """检测文件编码"""
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding']

def process_keywords_file(input_file: str, output_file: str):
    """处理关键词文件的主函数"""
    
    print("🚀 开始关键词自动分类...")
    
    # 初始化分类器
    classifier = BuzzcutKeywordClassifier()
    
    # 检测文件编码
    try:
        encoding = detect_file_encoding(input_file)
        print(f"🔍 检测到文件编码: {encoding}")
    except Exception as e:
        print(f"⚠️ 编码检测失败，尝试常见编码: {e}")
        encoding = None
    
    # 尝试不同编码读取CSV文件
    encodings_to_try = [encoding, 'utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252'] if encoding else ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
    
    df = None
    for enc in encodings_to_try:
        if enc is None:
            continue
        try:
            df = pd.read_csv(input_file, encoding=enc)
            print(f"✅ 成功使用 {enc} 编码读取 {len(df)} 个关键词")
            break
        except Exception as e:
            print(f"❌ 使用 {enc} 编码读取失败: {e}")
            continue
    
    if df is None:
        print("❌ 所有编码尝试失败，无法读取文件")
        return
    
    # 处理每个关键词
    results = []
    processed_count = 0
    error_count = 0
    
    for index, row in df.iterrows():
        try:
            keyword = str(row['Keyword']).strip()
            
            # 跳过空行或无效数据
            if pd.isna(keyword) or keyword == '' or keyword.lower() == 'nan':
                continue
                
            search_volume = int(float(row['Avg. monthly searches']))
            competition = float(row['Competition (indexed value)'])
            
            # 分类关键词
            classification = classifier.classify_keyword(keyword, search_volume, competition)
            
            # 转换为字典格式
            result = {
                'Keyword': classification.keyword,
                'Search_Volume': classification.search_volume,
                'Competition': classification.competition,
                'Primary_Category': classification.primary_category,
                'Demographics': classification.demographics,
                'Search_Intent': classification.search_intent,
                'Face_Shape': classification.face_shape,
                'Hair_Texture': classification.hair_texture,
                'Occasion': classification.occasion,
                'Target_Page': classification.target_page,
                'Priority': classification.priority,
                'Confidence_Score': round(classification.confidence_score, 3)
            }
            
            results.append(result)
            processed_count += 1
            
            # 进度显示
            if processed_count % 500 == 0:
                print(f"⏳ 已处理 {processed_count}/{len(df)} 个关键词...")
                
        except Exception as e:
            error_count += 1
            keyword_name = row.get('Keyword', 'Unknown') if isinstance(row.get('Keyword'), str) else 'Unknown'
            print(f"⚠️ 处理关键词 '{keyword_name}' 时出错: {e}")
            if error_count > 10:  # 如果错误太多，只显示前10个
                print("...")
            continue
    
    if not results:
        print("❌ 没有成功处理任何关键词")
        return
    
    # 保存结果
    try:
        result_df = pd.DataFrame(results)
        result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        # 生成分析报告
        generate_analysis_report(result_df, output_file.replace('.csv', '_analysis.txt'))
        
        print(f"✅ 分类完成！结果已保存到: {output_file}")
        print(f"📈 成功处理 {len(results)} 个关键词")
        if error_count > 0:
            print(f"⚠️ 跳过 {error_count} 个有问题的关键词")
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")

def generate_analysis_report(df: pd.DataFrame, report_file: str):
    """生成分析报告"""
    
    try:
        report = []
        report.append("📊 关键词分类分析报告")
        report.append("=" * 50)
        
        # 主分类统计
        report.append("\n🎯 主分类分布:")
        primary_stats = df['Primary_Category'].value_counts()
        for category, count in primary_stats.items():
            percentage = (count / len(df)) * 100
            report.append(f"  {category}: {count} ({percentage:.1f}%)")
        
        # 人群分布统计
        report.append("\n👥 目标人群分布:")
        demo_stats = df['Demographics'].value_counts()
        for demo, count in demo_stats.items():
            percentage = (count / len(df)) * 100
            report.append(f"  {demo}: {count} ({percentage:.1f}%)")
        
        # 搜索意图分布
        report.append("\n🔍 搜索意图分布:")
        intent_stats = df['Search_Intent'].value_counts()
        for intent, count in intent_stats.items():
            percentage = (count / len(df)) * 100
            report.append(f"  {intent}: {count} ({percentage:.1f}%)")
        
        # 优先级分布
        report.append("\n⭐ 优先级分布:")
        priority_stats = df['Priority'].value_counts()
        for priority, count in priority_stats.items():
            percentage = (count / len(df)) * 100
            report.append(f"  {priority}: {count} ({percentage:.1f}%)")
        
        # 高价值关键词Top 20
        report.append("\n🏆 高价值关键词 (Top 20):")
        high_value = df.nlargest(20, 'Search_Volume')[['Keyword', 'Search_Volume', 'Competition', 'Priority']]
        for _, row in high_value.iterrows():
            report.append(f"  {row['Keyword']} - {row['Search_Volume']:,} 搜索量, 竞争度{row['Competition']}, {row['Priority']}优先级")
        
        # 置信度统计
        avg_confidence = df['Confidence_Score'].mean()
        report.append(f"\n📊 平均分类置信度: {avg_confidence:.3f}")
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print(f"📋 分析报告已保存到: {report_file}")
        
    except Exception as e:
        print(f"⚠️ 生成分析报告时出错: {e}")

if __name__ == "__main__":
    # 安装必要的包
    try:
        import chardet
    except ImportError:
        print("📦 正在安装 chardet 包...")
        import subprocess
        subprocess.check_call(['pip', 'install', 'chardet'])
        import chardet
    
    # 使用示例
    input_file = "doc/谷歌关键词规划+综合.csv"
    output_file = "doc/关键词分类结果.csv"
    
    process_keywords_file(input_file, output_file) 