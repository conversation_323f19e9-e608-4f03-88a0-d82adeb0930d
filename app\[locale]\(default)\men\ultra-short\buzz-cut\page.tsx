import { Metadata } from 'next';
import { getBuzzCutPage } from '@/services/page';
import HeroAI from '@/components/blocks/AIHair/HeroAI';
import HairstyleCategories from '@/components/blocks/AIHair/HairstyleCategories';
import PopularHairStyles from '@/components/blocks/AIHair/PopularHairStyles';
import FeaturesAI from '@/components/blocks/AIHair/FeaturesAI';
import TrendingStyles from '@/components/blocks/AIHair/TrendingStyles';
import FaceShapeMatcher from '@/components/blocks/AIHair/FaceShapeMatcher';
import HairstyleTools from '@/components/blocks/AIHair/HairstyleTools';
import Feature1 from '@/components/blocks/feature1';
import Feature2 from '@/components/blocks/feature2';
import FAQ from '@/components/blocks/faq';
import CTA from '@/components/blocks/cta';
import Showcase from '@/components/blocks/showcase';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const page = await getBuzzCutPage(locale);
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/men/ultra-short/buzz-cut`;
  if (locale !== 'en') canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/men/ultra-short/buzz-cut`;

  return {
    title: page.meta.title,
    description: page.meta.description,
    //keywords: page.meta.keywords,
    alternates: { canonical: canonicalUrl },
    openGraph: {
      title: page.meta.title,
      description: page.meta.description,
      images: ['/imgs/men/ultra-short/buzz-cut/hero.webp'],
      url: canonicalUrl,
    },
  };
}

export default async function BuzzCutPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getBuzzCutPage(locale);

  // Hero 区域数据
  const heroData = {
    title: page.hero.title,
    subtitle: page.hero.subtitle,
    description: page.hero.description,
    primaryAction: { text: page.hero.primaryCTA, href: '/#aiTryOn' },
    secondaryAction: { text: page.hero.secondaryCTA, href: '#styles' },
    image: '/imgs/men/ultra-short/buzz-cut/hero.webp',
    stats: page.hero.stats,
  };

  // 介绍部分数据
  const introductionData = {
    title: page.introduction.title,
    description: page.introduction.content,
    image: {
      src: '/imgs/men/ultra-short/buzz-cut/introduction.webp',
      alt: 'What is a Buzz Cut for Men'
    },
    items: page.introduction.highlights.map((highlight: any) => ({
      title: highlight,
      description: `Key benefit of buzz cut hair men styling`,
      icon: "check",
    }))
  };

  // Guard 指南数据 - 改用 Feature1
  const guardGuideData = {
    title: page.guardGuide.title,
    subtitle: page.guardGuide.subtitle, 
    description: page.guardGuide.description,
    image: {
      src: '/imgs/tools/guard-size-chart.webp',
      alt: 'Buzz Cut Hair Clippers Guard Size Chart'
    },
    items: page.guardGuide.items.map((guard: any) => ({
      title: `${guard.guardNumber} - ${guard.length}`,
      description: `${guard.description}. Best for: ${guard.bestFor}. Maintenance: ${guard.maintenance}`,
      icon: "scissors",
    }))
  };

  // AI 功能数据
  const aiFeaturesData = page.aiFeatures.items.map((feature: any) => ({
    ...feature,
    icon: `/imgs/shared/icons/${feature.icon}.webp`,
  }));

  // 名人发型数据
  const celebrityData = page.celebrityBuzzCuts.items.map((celebrity: any) => ({
    id: celebrity.id,
    title: celebrity.title,
    description: `${celebrity.description} Style: ${celebrity.style}, Guard: ${celebrity.guardSize}`,
    image: celebrity.image,
    url: `#${celebrity.id}`,
    tags: [celebrity.style, celebrity.guardSize]
  }));

  // 维护指南数据
  const maintenanceData = {
    title: page.maintenance.title,
    subtitle: page.maintenance.subtitle,
    description: page.maintenance.description,
    sections: page.maintenance.sections
  };

  // 工具推荐数据
  const toolsData = page.tools.items.map((tool: any) => ({
    id: tool.id,
    name: tool.title,
    description: `${tool.description} Features: ${tool.features.join(', ')}`,
    image: tool.image,
    price: tool.price,
    rating: tool.rating,
    url: `#${tool.id}`,
  }));

  // 脸型指南数据
  const faceShapeData = page.faceShapeGuide.items.map((item: any) => ({
    id: item.faceShape.toLowerCase().replace(/\s+/g, '-'),
    title: item.title,
    description: `${item.description} Recommended: ${item.recommendedStyles.join(', ')}. Tips: ${item.tips}`,
    image: `/imgs/face/${item.faceShape.toLowerCase().replace(/\s+/g, '-')}.webp`,
    tags: item.recommendedStyles,
  }));

  // DIY 指南数据 - 改用 Feature1
  const howToGuideData = {
    title: page.howToGuide.title,
    subtitle: page.howToGuide.subtitle,
    description: page.howToGuide.description,
    image: {
      src: '/imgs/men/ultra-short/buzz-cut/diy-guide.webp', // 一张理发过程图
      alt: 'How to Cut a Buzz Cut - DIY Guide'
    },
    items: page.howToGuide.steps.map((step: any) => ({
      title: `Step ${step.step}: ${step.title}`,
      description: `${step.description} Tips: ${step.tips.join(', ')}`,
      icon: step.step === 1 ? "settings" : step.step === 2 ? "play" : step.step === 3 ? "repeat" : "check",
    }))
  };


  // 优势数据
  const benefitsData = page.benefits.items.map((benefit: any) => ({
    ...benefit,
    icon: `/imgs/shared/icons/${benefit.icon}.webp`,
  }));

  return (
    <main className="min-h-screen">
      {/* Hero */}
      <HeroAI section={heroData} />

      {/* 介绍部分 */}
      <section className="py-16 bg-white">
        <Feature1 section={introductionData} />
      </section>

      {/* Buzz Cut 样式展示 - 使用 Showcase 组件，数据驱动 */}
      <section id="styles" className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto mb-16">
            <p className="text-xl text-slate-600 mb-4">
              {page.showcase?.subtitle}
            </p>
          </div>
          {page.showcase && <Showcase section={page.showcase} />}
        </div>
      </section>

      {/* Guard 指南 */}
      <section className="py-16 bg-white">
        <Feature1 section={guardGuideData} />
      </section>

      {/* AI 功能 */}
      <section className="py-20 bg-slate-50">
        <FeaturesAI section={{
          title: page.aiFeatures.title,
          subtitle: page.aiFeatures.subtitle,
          description: page.aiFeatures.description,
          features: aiFeaturesData,
        }} />
      </section>

      {/* 名人发型 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
              {page.celebrityBuzzCuts.title}
            </h2>
            <p className="text-xl text-slate-600 mb-4">
              {page.celebrityBuzzCuts.subtitle}
            </p>
          </div>
          <TrendingStyles section={{
            title: '',
            items: celebrityData,
            viewAllLink: '#celebrities',
          }} />
        </div>
      </section>

      {/* 维护指南 */}
      <section className="py-16 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
                {maintenanceData.title}
              </h2>
              <p className="text-xl text-slate-600 mb-4">
                {maintenanceData.subtitle}
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {maintenanceData.sections.map((section: any, index: number) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-sm">
                  <h3 className="text-xl font-semibold mb-4 text-slate-900">
                    {section.title}
                  </h3>
                  <ul className="space-y-2">
                    {section.items.map((item: any, itemIndex: number ) => (
                      <li key={itemIndex} className="text-slate-600 flex items-start">
                        <span className="text-blue-600 mr-2">•</span>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 工具推荐 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <HairstyleTools section={{
            title: page.tools.title,
            subtitle: page.tools.subtitle,
            description: page.tools.description,
            items: toolsData,
          }} />
        </div>
      </section>

      {/* 脸型指南 */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
              {page.faceShapeGuide.title}
            </h2>
            <p className="text-xl text-slate-600 mb-4">
              {page.faceShapeGuide.subtitle}
            </p>
          </div>
          <TrendingStyles section={{
            title: '',
            items: faceShapeData,
            viewAllLink: '/men',
          }} />
        </div>
      </section>

      {/* DIY 指南 */}
      <section className="py-16 bg-white">
        <Feature1 section={howToGuideData} />
      </section>

      {/* 优势介绍 */}
      <section className="py-20 bg-slate-50">
        <FeaturesAI section={{
          title: page.benefits.title,
          subtitle: page.benefits.subtitle,
          description: page.benefits.description,
          features: benefitsData,
        }} />
      </section>

      {/* FAQ */}
      <section className="py-20 bg-white">
        <FAQ section={{
          title: page.faq.title,
          description: page.faq.subtitle,
          items: page.faq.items.map((item: any) => ({
            title: item.question,
            description: item.answer
          }))
        }} />
      </section>

      {/* CTA */}
      <section className="py-20 bg-slate-50">
        <CTA section={{
          title: page.cta.title,
          description: page.cta.description,
          buttons: [
            { title: page.cta.primaryButton, url: '/#aiTryOn' },
            { title: page.cta.secondaryButton, url: '/men' }
          ]
        }} />
      </section>
    </main>
  );
} 