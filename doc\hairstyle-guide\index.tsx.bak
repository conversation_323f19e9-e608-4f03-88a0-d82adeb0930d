import Image from "next/image";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Users, Scissors, Ruler } from "lucide-react";

interface HairstyleGuideProps {
  section: {
    title: string;
    subtitle?: string;
    steps: Array<{
      id: string;
      title: string;
      description: string;
      image?: string;
      tips?: string[];
      tools?: string[];
      duration?: string;
    }>;
    specifications?: {
      title: string;
      details: Array<{
        label: string;
        value: string;
        icon?: string;
      }>;
    };
    professionalTips?: {
      title: string;
      tips: string[];
    };
  };
}

const iconMap = {
  clock: Clock,
  users: Users,
  scissors: Scissors,
  ruler: Ruler,
};

export default function HairstyleGuide({ section }: HairstyleGuideProps) {
  return (
    <section className="py-16 px-4 bg-white">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {section.title}
          </h2>
          {section.subtitle && (
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {section.subtitle}
            </p>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Steps */}
          <div className="lg:col-span-2">
            <div className="space-y-8">
              {section.steps.map((step, index) => (
                <Card key={step.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      {/* Step Number */}
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold text-sm">
                        {index + 1}
                      </div>

                      <div className="flex-1 space-y-4">
                        {/* Step Header */}
                        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                          <div className="flex-1">
                            <h3 className="text-xl font-semibold text-gray-900 mb-2">
                              {step.title}
                            </h3>
                            <p className="text-gray-600">
                              {step.description}
                            </p>
                          </div>

                          {step.duration && (
                            <Badge variant="outline" className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {step.duration}
                            </Badge>
                          )}
                        </div>

                        {/* Step Image */}
                        {step.image && (
                          <div className="relative h-48 rounded-lg overflow-hidden">
                            <Image
                              src={step.image}
                              alt={step.title}
                              fill
                              className="object-cover"
                            />
                          </div>
                        )}

                        {/* Tools */}
                        {step.tools && step.tools.length > 0 && (
                          <div className="space-y-2">
                            <h4 className="font-medium text-gray-900 flex items-center gap-2">
                              <Scissors className="w-4 h-4" />
                              Tools Needed:
                            </h4>
                            <div className="flex flex-wrap gap-2">
                              {step.tools.map((tool, idx) => (
                                <Badge key={idx} variant="secondary">
                                  {tool}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Tips */}
                        {step.tips && step.tips.length > 0 && (
                          <div className="bg-blue-50 p-4 rounded-lg">
                            <h4 className="font-medium text-blue-900 mb-2">
                              💡 Pro Tips:
                            </h4>
                            <ul className="space-y-1 text-sm text-blue-800">
                              {step.tips.map((tip, idx) => (
                                <li key={idx} className="flex items-start gap-2">
                                  <span className="text-blue-500 mt-1">•</span>
                                  <span>{tip}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Specifications */}
            {section.specifications && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {section.specifications.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {section.specifications.details.map((detail, index) => {
                    const IconComponent = detail.icon ? iconMap[detail.icon as keyof typeof iconMap] : null;
                    return (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {IconComponent && <IconComponent className="w-4 h-4 text-gray-500" />}
                          <span className="text-gray-600">{detail.label}</span>
                        </div>
                        <span className="font-medium text-gray-900">{detail.value}</span>
                      </div>
                    );
                  })}
                </CardContent>
              </Card>
            )}

            {/* Professional Tips */}
            {section.professionalTips && (
              <Card className="bg-gradient-to-br from-amber-50 to-orange-50 border-amber-200">
                <CardHeader>
                  <CardTitle className="text-lg text-amber-900">
                    {section.professionalTips.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {section.professionalTips.tips.map((tip, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-amber-800 text-sm">
                          {tip}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
