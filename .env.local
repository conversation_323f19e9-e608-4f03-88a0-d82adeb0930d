# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME = "BuzzcutAI"
NEXT_PUBLIC_SHOW_POWERED_BY = "false"
# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# Set your Supabase URL and Anon Key
SUPABASE_URL = "https://kxhffkcqeqjspjpbzcht.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt4aGZma2NxZXFqc3BqcGJ6Y2h0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMTY3MTQsImV4cCI6MjA2Mzg5MjcxNH0.q4pZ5DZhz89_WW5v1h53cv999BUOph-DJBk93DR4nxE"
SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt4aGZma2NxZXFqc3BqcGJ6Y2h0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODMxNjcxNCwiZXhwIjoyMDYzODkyNzE0fQ._zaBSTi4eTxNPj827_J5usAwFLPjBlysTYYrUW1tDIg"

# -----------------------------------------------------------------------------
# Auth with next-auth
# https://authjs.dev/getting-started/installation?framework=Next.js
# Set your Auth URL and Secret
# Secret can be generated with `openssl rand -base64 32`
# 或gpt提示词:你帮我使用 openssl rand -base64 32 命令生成的一个新的随机密钥
# -----------------------------------------------------------------------------
AUTH_SECRET = "3Y1zv5OgH+Z8AsvRA4j7gSvBHz9NBmOqfTkaBHz9nkM="

# Google Auth
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID = "447882794353-4ti7qunvm1glclbhgdbdmgm8gqun804v.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET = "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"
NEXT_PUBLIC_AUTH_GOOGLE_ID = "447882794353-4ti7qunvm1glclbhgdbdmgm8gqun804v.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "true"
# Github Auth
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID = " "
AUTH_GITHUB_SECRET = " "
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# https://analytics.google.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = ""
# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# https://openpanel.dev
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""
# -----------------------------------------------------------------------------

# -----------------------------------------------------------------------------
# Analytics with Microsoft Clarity配置
# https://clarity.microsoft.com/
# -----------------------------------------------------------------------------
NEXT_PUBLIC_CLARITY_PROJECT_ID=s8w4makrbl
# -----------------------------------------------------------------------------
# Payment with Creem.io (替代 Stripe)
# https://docs.creem.io
# -----------------------------------------------------------------------------
# Stripe 配置（已弃用，保留以防回滚）
STRIPE_PUBLIC_KEY = ""
STRIPE_PRIVATE_KEY = ""
STRIPE_WEBHOOK_SECRET = ""

# Creem.io 支付配置
CREEM_API_KEY = creem_test_642WPrLjYz4yUzNmFR0qaH
CREEM_API_URL = https://test-api.creem.io
CREEM_MODE = test   
CREEM_WEBHOOK_SECRET = whsec_65vGnMOVw9cyRVuGO9cNLp

# 产品配置 - 需要在 Creem.io 后台创建对应的产品
# Free 方案（免费版，特殊处理）
FREE_PLAN_PRODUCT_ID = free_plan_product_id_placeholder
# Plus 方案 ($12/月, 5M字符)
PLUS_PLAN_PRODUCT_ID = prod_2vDA0uDrNE423H36o6a3Km   
# Pro 方案 ($25/月, 15M字符)
PRO_PLAN_PRODUCT_ID = prod_5hYNGlM2t3DdeKqs6AjW5K

# 支付页面配置
NEXT_PUBLIC_PAY_SUCCESS_URL = /creem-success
NEXT_PUBLIC_PAY_FAIL_URL = /creem-fail
NEXT_PUBLIC_PAY_CANCEL_URL = /


NEXT_PUBLIC_LOCALE_DETECTION = "false"

ADMIN_EMAILS = "<EMAIL>"

NEXT_PUBLIC_DEFAULT_THEME = "light"

# -----------------------------------------------------------------------------
# Storage with aws s3 sdk
# https://docs.aws.amazon.com/s3/index.html
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = ""
STORAGE_REGION = ""
STORAGE_ACCESS_KEY = ""
STORAGE_SECRET_KEY = ""
STORAGE_BUCKET = ""
STORAGE_DOMAIN = ""

# -----------------------------------------------------------------------------
# AI 服务配置
# -----------------------------------------------------------------------------
OPENAI_API_KEY = your_openai_api_key
OPENROUTER_API_KEY = "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"
REPLICATE_API_TOKEN = "****************************************"

# -----------------------------------------------------------------------------
# 邮件服务配置 (可选)
# -----------------------------------------------------------------------------
SMTP_HOST = your_smtp_host
SMTP_PORT = 587
SMTP_USER = your_smtp_user
SMTP_PASS = your_smtp_password

# -----------------------------------------------------------------------------
# 文件上传配置
# -----------------------------------------------------------------------------
MAX_FILE_SIZE = 20971520
ALLOWED_FILE_TYPES = application/pdf,text/plain

# -----------------------------------------------------------------------------
# 其他配置,好像不需要
# -----------------------------------------------------------------------------
#NODE_ENV = development
#NEXTAUTH_SECRET = your_nextauth_secret
#NEXTAUTH_URL = http://localhost:3000



