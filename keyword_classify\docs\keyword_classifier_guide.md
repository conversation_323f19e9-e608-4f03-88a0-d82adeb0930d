# 🔧 通用关键词分类算法框架使用指南

## 📋 概述

这是一个高度可复用的关键词分类算法框架，专门用于将大量关键词自动分配到不同的页面或类别中。该框架已经在Buzzcut AI项目中成功处理了6442个关键词的分类任务。

## 🎯 核心特点

- **🔧 高度可配置**: 支持精确匹配、正则表达式匹配、搜索量阈值设置
- **📊 智能评分**: 基于匹配度、优先级、搜索量的综合评分系统
- **🚀 批量处理**: 支持大规模关键词批量分类（已验证6000+词汇）
- **📈 统计分析**: 提供详细的分类统计和覆盖率分析
- **💾 多种导出**: 支持表格格式和平铺格式导出
- **🌍 多语言**: 支持中英文等多语言关键词处理
- **⚙️ 配置驱动**: 支持JSON配置文件驱动的规则管理

## 📁 文件结构

```
utils/
└── keyword_classifier_framework.py    # 核心框架代码
examples/
└── keyword_usage_example.py          # 使用示例
docs/
└── keyword_classifier_guide.md       # 本文档
```

## 🚀 快速开始

### 1. 基本使用

```python
from utils.keyword_classifier_framework import (
    KeywordClassificationFramework,
    ClassificationRule,
    DataProcessor,
    ResultExporter,
    KeywordData
)

# 创建分类器
classifier = KeywordClassificationFramework()

# 添加分类规则
rule = ClassificationRule(
    page_url="/category/electronics/",
    description="电子产品",
    exact_keywords=["iphone", "samsung", "laptop"],
    regex_patterns=[r'\biphone\b', r'\blaptop\b'],
    volume_threshold=100,
    max_keywords=50,
    priority=1
)
classifier.add_rule(rule)

# 设置兜底规则
fallback = ClassificationRule(
    page_url="/other/",
    description="其他",
    regex_patterns=[r'.*'],
    volume_threshold=1,
    max_keywords=10000,
    priority=999
)
classifier.set_fallback_rule(fallback)

# 准备数据
keywords = [
    KeywordData("iphone 15", 1000000, 8.5),
    KeywordData("samsung galaxy", 800000, 7.2)
]

# 执行分类
results = classifier.classify_batch(keywords)

# 导出结果
ResultExporter.export_to_csv(results, "classification_results.csv")
```

### 2. 从CSV文件加载数据

```python
# 从CSV加载关键词数据
keywords_data = DataProcessor.load_keywords_from_csv(
    "keywords.csv",
    keyword_column="Keyword",
    volume_column="Avg. monthly searches",
    competition_column="Competition (indexed value)"
)

# 执行分类
results = classifier.classify_batch(keywords_data)
```

## 🏗️ 核心组件详解

### ClassificationRule - 分类规则

每个分类规则定义了关键词如何被分配到特定页面：

```python
rule = ClassificationRule(
    page_url="/target/page/",           # 目标页面URL
    description="页面描述",              # 页面描述
    exact_keywords=["word1", "word2"],  # 精确匹配的关键词
    regex_patterns=[r'pattern1'],       # 正则表达式模式
    volume_threshold=100,               # 最低搜索量阈值
    max_keywords=50,                    # 最大关键词数量
    priority=1                          # 优先级（数字越小优先级越高）
)
```

**参数说明:**
- `page_url`: 关键词将被分配到的目标页面
- `description`: 页面的可读描述
- `exact_keywords`: 精确匹配的关键词列表（100分）
- `regex_patterns`: 正则表达式模式列表（30分）
- `volume_threshold`: 只处理搜索量≥此值的关键词
- `max_keywords`: 此规则最多分配的关键词数量
- `priority`: 规则优先级，用于解决冲突

### 评分机制

算法使用以下评分系统：
1. **精确匹配**: 100分
2. **包含匹配**: 50分  
3. **正则匹配**: 30分
4. **优先级调整**: 分数除以优先级值

### KeywordData - 关键词数据结构

```python
@dataclass
class KeywordData:
    keyword: str                      # 关键词
    search_volume: int               # 搜索量
    competition: float               # 竞争度
    additional_data: Dict[str, Any]  # 额外数据
```

### ClassificationResult - 分类结果

```python
@dataclass
class ClassificationResult:
    keyword: str           # 关键词
    search_volume: int     # 搜索量
    competition: float     # 竞争度
    target_page: str       # 目标页面
    page_description: str  # 页面描述
    match_score: float     # 匹配分数
    primary_category: str  # 主分类
    confidence: float      # 置信度
```

## 💡 实际应用案例

### 案例1: 电商网站分类

```python
def setup_ecommerce_classifier():
    classifier = KeywordClassificationFramework()
    
    # 手机类别
    mobile_rule = ClassificationRule(
        page_url="/electronics/mobile/",
        description="手机专区",
        exact_keywords=["iphone", "samsung", "xiaomi"],
        regex_patterns=[r'\biphone\b', r'\bphone\b'],
        volume_threshold=100,
        max_keywords=100,
        priority=1
    )
    classifier.add_rule(mobile_rule)
    
    # 服装类别
    clothing_rule = ClassificationRule(
        page_url="/fashion/clothing/",
        description="服装专区",
        exact_keywords=["dress", "shirt", "shoes"],
        regex_patterns=[r'\bdress\b', r'\bshirt\b'],
        volume_threshold=50,
        max_keywords=150,
        priority=2
    )
    classifier.add_rule(clothing_rule)
    
    return classifier
```

### 案例2: 旅游网站分类

```python
def setup_travel_classifier():
    classifier = KeywordClassificationFramework()
    
    # 目的地
    destination_rule = ClassificationRule(
        page_url="/destinations/",
        description="旅游目的地",
        exact_keywords=["paris", "london", "tokyo"],
        regex_patterns=[r'\bparis\b', r'\blondon\b'],
        volume_threshold=50,
        max_keywords=200,
        priority=1
    )
    classifier.add_rule(destination_rule)
    
    # 酒店
    hotel_rule = ClassificationRule(
        page_url="/hotels/",
        description="酒店预订",
        exact_keywords=["hotel", "resort", "booking"],
        regex_patterns=[r'\bhotel\b', r'\bresort\b'],
        volume_threshold=100,
        max_keywords=150,
        priority=2
    )
    classifier.add_rule(hotel_rule)
    
    return classifier
```

### 案例3: Buzzcut AI 项目实际配置

```python
def setup_buzzcut_classifier():
    """Buzzcut AI 实际使用的分类配置"""
    classifier = KeywordClassificationFramework()
    
    # 首页 - 最高价值关键词
    homepage_rule = ClassificationRule(
        page_url="/",
        description="首页",
        exact_keywords=["buzz cut", "hair style", "haircuts"],
        regex_patterns=[r'^(buzz cut|hair style|haircuts)$'],
        volume_threshold=1000000,  # 100万搜索量
        max_keywords=10,
        priority=1
    )
    classifier.add_rule(homepage_rule)
    
    # Buzz Cut 主页面
    buzzcut_rule = ClassificationRule(
        page_url="/men/ultra-short/buzz-cut/",
        description="Buzz Cut 主页面",
        exact_keywords=["buzz cut", "buzz cut for men"],
        regex_patterns=[r'\bbuzz\s*cut\b(?!.*fade)'],
        volume_threshold=1000,
        max_keywords=30,
        priority=2
    )
    classifier.add_rule(buzzcut_rule)
    
    # 渐变发型
    fade_rule = ClassificationRule(
        page_url="/men/techniques/fade/",
        description="渐变发型",
        exact_keywords=["fade haircut", "low fade", "high fade"],
        regex_patterns=[r'\bfade\b(?!.*buzz)', r'\btaper\s*fade\b'],
        volume_threshold=500,
        max_keywords=50,
        priority=3
    )
    classifier.add_rule(fade_rule)
    
    return classifier
```

## 📊 导出格式

### 表格格式 (Table Format)
类似Excel表格，按页面分组显示：

```csv
主关键词,搜索量,KD值,页面,话题,大纲
=== 首页 (/) ===,共5个词,总量25M,,,
buzz cut,5M,0,/,首页,
hair style,5M,10,,,
...
```

### 平铺格式 (Flat Format)
每行一个关键词的详细信息：

```csv
keyword,search_volume,competition,target_page,page_description,match_score,confidence
buzz cut,5000000,0,/,首页,100.0,1.0
hair style,5000000,10,/,首页,100.0,1.0
...
```

## 🔧 高级功能

### 1. 统计分析

```python
# 获取分类统计
stats = classifier.get_statistics()

print(f"总处理数: {stats['total_processed']}")
print(f"成功分类: {stats['total_assigned']}")
print(f"覆盖率: {stats['coverage_rate']:.1%}")

# 查看各规则的使用情况
for rule_stat in stats['rule_statistics']:
    print(f"页面: {rule_stat['page_url']}")
    print(f"分配词数: {rule_stat['assigned_count']}")
    print(f"利用率: {rule_stat['utilization']:.1%}")
```

### 2. 数据处理工具

```python
# 安全数据转换
safe_volume = DataProcessor.safe_convert_to_int("1000000", 0)
safe_competition = DataProcessor.safe_convert_to_float("8.5", 0.0)

# 关键词清理
cleaned_keyword = DataProcessor.clean_keyword("  Buzz Cut  ")
```

### 3. 多种导出选项

```python
# 表格格式导出
ResultExporter.export_to_csv(results, "table_format.csv", "table")

# 平铺格式导出
ResultExporter.export_to_csv(results, "flat_format.csv", "flat")

# 生成汇总报告
ResultExporter.generate_summary_report(results, stats, "summary.txt")
```

## 🛠️ 自定义扩展

### 1. 自定义评分规则

继承 `ClassificationRule` 类并重写 `calculate_match_score` 方法：

```python
class CustomClassificationRule(ClassificationRule):
    def calculate_match_score(self, keyword: str, search_volume: int) -> float:
        base_score = super().calculate_match_score(keyword, search_volume)
        
        # 添加自定义逻辑
        if "premium" in keyword.lower():
            base_score *= 1.5  # 优质词汇加分
        
        return min(base_score, 100.0)
```

### 2. 自定义数据加载器

```python
class CustomDataProcessor(DataProcessor):
    @staticmethod
    def load_from_api(api_endpoint: str) -> List[KeywordData]:
        # 从API加载数据
        response = requests.get(api_endpoint)
        data = response.json()
        
        keywords_data = []
        for item in data:
            keywords_data.append(KeywordData(
                keyword=item['keyword'],
                search_volume=item['volume'],
                competition=item['difficulty']
            ))
        
        return keywords_data
```

## 📈 性能优化建议

1. **批量处理**: 使用 `classify_batch()` 而不是循环调用 `classify_keyword()`
2. **规则排序**: 将高频匹配的规则设置更高优先级
3. **正则优化**: 避免复杂的正则表达式，使用简单高效的模式
4. **内存管理**: 对于超大数据集，考虑分批处理

## 🐛 常见问题

### Q: 关键词分配不均衡怎么办？
A: 调整各规则的 `max_keywords` 参数和 `volume_threshold` 阈值。

### Q: 某些关键词没有被分类？
A: 检查是否设置了合适的兜底规则，确保兜底规则的阈值足够低。

### Q: 分类准确率不高？
A: 优化正则表达式模式，增加精确匹配关键词，调整优先级。

### Q: 处理大量数据时性能慢？
A: 使用批量处理，简化正则表达式，减少规则数量。

## 📝 更新日志

- **v1.0** (2024): 初始版本，支持基本分类功能
- 成功处理Buzzcut AI项目的6442个关键词分类任务
- 达到78%的分类覆盖率
- 支持多种导出格式

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个框架！

## 📄 许可证

MIT License - 可自由用于商业和非商业项目。 