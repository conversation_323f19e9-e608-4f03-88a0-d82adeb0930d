// app/[locale]/(default)/men/specialty/page.tsx
import { Metadata } from 'next';
import { getSpecialtyPage } from '@/services/page';
import HeroAI from '@/components/blocks/AIHair/HeroAI';
import HairstyleCategories from '@/components/blocks/AIHair/HairstyleCategories';
import PopularHairStyles from '@/components/blocks/AIHair/PopularHairStyles';
import FeaturesAI from '@/components/blocks/AIHair/FeaturesAI';
import Feature from '@/components/blocks/feature';
import Feature1 from '@/components/blocks/feature1';
import Feature2 from '@/components/blocks/feature2';
import HairstyleTools from '@/components/blocks/AIHair/HairstyleTools';
import TrendingStyles from '@/components/blocks/AIHair/TrendingStyles';
import FAQ from '@/components/blocks/faq';
import CTA from '@/components/blocks/cta';
// import Crumb from "@/components/blocks/crumb";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const page = await getSpecialtyPage(locale);
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/men/specialty`;
  if (locale !== 'en') canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/men/specialty`;

  return {
    title: page.meta.title,
    description: page.meta.description,
    keywords: page.meta.keywords,
    alternates: { canonical: canonicalUrl },
    openGraph: {
      title: page.meta.title,
      description: page.meta.description,
      images: ['/imgs/men/specialty/hero.webp'],
      url: canonicalUrl,
    },
  };
}

export default async function SpecialtyPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getSpecialtyPage(locale);

  // ✅ 从导航数据中获取正确的文案 - 暂时注释
  // const navItems = page.navigation || [];
  // const menNavItem = navItems.find((item: any) => item.url === "/men");
  // const ultraShortNavItem = menNavItem?.children?.find((item: any) => item.url === "/men/ultra-short");
  
  // const breadcrumbItems = [
  //   {
  //     title: "Home",
  //     url: locale === "en" ? "/" : `/${locale}`,
  //     is_active: false
  //   },
  //   {
  //     title: menNavItem?.title || "Men's Styles", // ✅ 从导航配置获取
  //     url: locale === "en" ? "/men" : `/${locale}/men`,
  //     is_active: false
  //   },
  //   {
  //     title: ultraShortNavItem?.title || "Ultra Short Haircuts", // ✅ 从导航配置获取
  //     url: "",
  //     is_active: true
  //   }
  // ];

  // Hero 区域数据
  const heroData = {
    title: page.hero.title,
    subtitle: page.hero.subtitle,
    description: page.hero.description,
    primaryAction: { text: page.hero.primaryCTA, href: '/#aiTryOn' },
    secondaryAction: { text: page.hero.secondaryCTA, href: '#categories' },
    image: page.hero.image.src,
  };

  // 子分类导航数据 - 修复字段映射
  const categoriesData = {
    title: page.categories.title,
    subtitle: page.categories.subtitle,
    description: page.categories.description,
    categories: page.categories.items.map((item: any) => ({
      id: item.id,
      name: item.title, // 🎯 关键修复：将 title 映射为 name
      description: item.description,
      image: item.image.src,
      count: 12, // 添加缺失的 count 字段
      url: item.url,
    })),
  };

  // 热门发型数据 - 确保字段正确
  const popularData = page.popular.styles.map((style: any) => ({
    id: style.id,
    title: style.title,
    description: style.description,
    image: style.image.src,
    url: style.url,
    tags: style.tags,
  }));

  // Why Specialty 数据
  const whySpecialtyData = {
    title: page.whySpecialty.title,
    subtitle: page.whySpecialty.subtitle,
    description: page.whySpecialty.description,
    image: page.whySpecialty.image,
    items: page.whySpecialty.benefits,
  };

  // Celebrity Inspiration 数据
  const celebrityData = page.celebrityInspiration.items.map((celebrity: any) => ({
    id: celebrity.id,
    title: celebrity.title,
    description: `${celebrity.description} Style: ${celebrity.style}`,
    image: celebrity.image.src,
    url: `#${celebrity.id}`,
    tags: [celebrity.style, celebrity.inspiration]
  }));

  // AI 功能数据
  const aiFeaturesData = page.aiFeatures.items.map((feature: any) => ({
    ...feature,
    icon: `/imgs/shared/icons/${feature.icon}.webp`,
  }));

  // 造型产品数据
  const stylingProductsData = page.stylingProducts.items.map((product: any) => ({
    id: product.id,
    name: product.title,
    description: product.description,
    features: product.features,
    price: product.priceRange,
    rating: product.rating,
    image: product.image.src,
    bestFor: product.bestFor,
  }));

  // 护理指南数据
  const careGuideData = {
    title: page.careGuide.title,
    subtitle: page.careGuide.subtitle,
    description: page.careGuide.description,
    image: page.careGuide.image,
    items: page.careGuide.items,
  };

  // 工具推荐数据
  const toolsData = page.tools.items.map((tool: any) => ({
    id: tool.id,
    name: tool.title,
    description: tool.description,
    image: tool.image.src,
    price: tool.priceRange,
    rating: tool.rating,
    url: `#${tool.id}`,
  }));

  // 适合人群分析
  const audienceData = page.targetAudience.items.map((item: any) => ({
    id: item.category.toLowerCase().replace(/\s+/g, '-'),
    title: item.title,
    description: `${item.description} Benefits: ${item.benefits}`,
    image: item.image.src,
    tags: item.characteristics,
  }));

  return (
    <main className="min-h-screen">
      {/* ✅ 面包屑暂时注释 */}
      {/* <div className="bg-slate-50 border-b">
        <div className="container mx-auto px-4 py-4">
          <Crumb items={breadcrumbItems} />
        </div>
      </div> */}

      {/* Hero */}
      <HeroAI section={heroData} />

      {/* 子分类导航 */}
      <section id="categories" className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <HairstyleCategories section={categoriesData} />
        </div>
      </section>

      {/* Why Specialty */}
      <section className="py-16 bg-white">
        <Feature1 section={whySpecialtyData} />
      </section>

      {/* 热门发型 */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
              {page.popular.title}
            </h2>
            <p className="text-xl text-slate-600 mb-4">
              {page.popular.subtitle}
            </p>
          </div>
          <PopularHairStyles styles={popularData} />
        </div>
      </section>

      {/* Celebrity Inspiration */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
              {page.celebrityInspiration.title}
            </h2>
            <p className="text-xl text-slate-600 mb-4">
              {page.celebrityInspiration.subtitle}
            </p>
          </div>
          <TrendingStyles section={{
            title: '',
            items: celebrityData,
            viewAllLink: '#celebrities',
          }} />
        </div>
      </section>

      {/* AI 功能 */}
      <section className="py-20 bg-slate-50">
        <FeaturesAI section={{
          title: page.aiFeatures.title,
          subtitle: page.aiFeatures.subtitle,
          description: page.aiFeatures.description,
          features: aiFeaturesData,
        }} />
      </section>

      {/* 造型产品推荐 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <HairstyleTools section={{
            title: page.stylingProducts.title,
            subtitle: page.stylingProducts.subtitle,
            description: page.stylingProducts.description,
            items: stylingProductsData,
          }} />
        </div>
      </section>

      {/* 护理指南 */}
      <section className="py-16 bg-slate-50">
        <Feature1 section={careGuideData} />
      </section>

      {/* 工具推荐 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <HairstyleTools section={{
            title: page.tools.title,
            subtitle: page.tools.subtitle,
            description: page.tools.description,
            items: toolsData,
          }} />
        </div>
      </section>


      {/* 适合人群分析 */}
      <section className="py-16 bg-slate-50">
        <TrendingStyles section={{
          title: page.targetAudience.title,
          subtitle: page.targetAudience.subtitle,
          description: page.targetAudience.description,
          items: audienceData,
          viewAllLink: '/men',
        }} />
      </section>

        {/* FAQ */}
        <section className="py-16 bg-white">
        <FAQ section={{
          title: page.faq.title,
          description: page.faq.subtitle,
          items: page.faq.items.map((item: any) => ({
            title: item.question,    // 🎯 将 question 映射为 title
            description: item.answer // 🎯 将 answer 映射为 description
          })),
        }} />
      </section>

      {/* CTA */}
      <section className="py-16 bg-slate-50">
        <CTA section={{
          title: page.cta.title,
          description: page.cta.description,
          buttons: [
            { title: page.cta.primaryButton, url: '/#aiTryOn' },
            { title: page.cta.secondaryButton, url: '/men' },
          ],
        }} />
      </section>
    </main>
  );
}