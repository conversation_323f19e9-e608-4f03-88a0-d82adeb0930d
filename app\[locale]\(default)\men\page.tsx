// app/[locale]/(default)/men/page.tsx
import { Metadata } from 'next';
import { getMenPage } from "@/services/page";
import HeroAI from '@/components/blocks/AIHair/HeroAI';
import HairstyleCategories from '@/components/blocks/AIHair/HairstyleCategories';
import PopularHairStyles from '@/components/blocks/AIHair/PopularHairStyles';
import FeaturesAI from '@/components/blocks/AIHair/FeaturesAI';
import TrendingStyles from '@/components/blocks/AIHair/TrendingStyles';
import FaceShapeMatcher from '@/components/blocks/AIHair/FaceShapeMatcher';
import FAQ from "@/components/blocks/faq";
import CTA from "@/components/blocks/cta";
//import Crumb from "@/components/blocks/crumb";

interface CategoryItem {
  id: string;
  title: string;
  description: string;
  url: string;
  // 添加其他可能的字段
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const page = await getMenPage(locale);
  
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/men`;
  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/men`;
  }

  return {
    title: page.meta.title,
    description: page.meta.description,
    //keywords: page.meta.keywords,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: page.meta.title,
      description: page.meta.description,
      images: ["/imgs/hero/mens-hairstyles-hero.webp"],
      url: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
    }
  };
}

export default async function MensHairstylesPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getMenPage(locale);



  // 准备组件数据
  const heroData = {
    title: page.hero.title,
    subtitle: page.hero.subtitle,
    description: page.hero.description,
    primaryAction: { text: page.hero.primaryCTA, href: '/#aiTryOn' },
    secondaryAction: { text: page.hero.secondaryCTA, href: '#categories' },
    image: '/imgs/men/hero/mens-hairstyles-hero.webp'
  };

  const categoriesData = {
    title: page.categories.title,
    subtitle: page.categories.subtitle,
    description: page.categories.description,
    categories: page.categories.items.map((item: any) => ({
      ...item,
      image: `/imgs/men/categories/${item.id}.webp`
    }))
  };

  const popularStylesData = page.popular.styles.map((style: any) => ({
    ...style,
    image: `/imgs/men/popular/${style.id}.webp`
  }));

  const aiFeatures = page.aiFeatures.features.map((feature: any) => ({
    ...feature,
    highlight: feature.id === 'virtual-tryion'
  }));

  const trendingData = [
    { 
      id: "textured-crop",
      title: "Textured Crop", 
      description: "Modern textured crop haircut with layered styling perfect for contemporary men's grooming trends.",
      image: "/imgs/men/trending/textured-crop.webp",
      tags: ["Modern", "Textured", "Short"]
    },
    { 
      id: "mid-fade",
      title: "Mid Fade", 
      description: "Versatile mid fade haircut that works with most hair styles men prefer for professional settings.",
      image: "/imgs/men/trending/mid-fade.webp",
      tags: ["Fade", "Professional", "Versatile"]
    },
    { 
      id: "modern-buzz",
      title: "Modern Buzz Cut", 
      description: "Contemporary buzz cut variation with subtle length differences creating modern appeal.",
      image: "/imgs/men/trending/modern-buzz.webp",
      tags: ["Buzz Cut", "Modern", "Low Maintenance"]
    },
    { 
      id: "korean-style",
      title: "Korean Style", 
      description: "Trending K-pop inspired mens haircuts with textured layers and modern styling techniques.",
      image: "/imgs/men/trending/korean-style.webp",
      tags: ["Korean", "Trendy", "K-pop"]
    }
  ];

  return (
    <main className="min-h-screen">
      {/* ✅ 使用现有的面包屑组件
      <div className="bg-slate-50 border-b">
        <div className="container mx-auto px-4 py-4">
          <Crumb items={breadcrumbItems} />
        </div>
      </div> */}

      {/* Hero Section -  */}
      <HeroAI section={heroData} />
      
      {/* Introduction Section -  */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold mb-6 text-slate-900">
              {page.intro.title}
            </h2>
            <div className="text-lg text-slate-600 leading-relaxed">
              <p>{page.intro.content}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section id="categories" className="py-20 bg-slate-50">
        <HairstyleCategories section={categoriesData} />
      </section>

      {/* Popular Styles Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900">
              {page.popular.title}
            </h2>
            <p className="text-xl text-slate-600 mb-4">
              {page.popular.subtitle}
            </p>
            <p className="text-slate-500">
              {page.popular.description}
            </p>
          </div>
          
          <PopularHairStyles styles={popularStylesData} />
        </div>
      </section>

      {/* AI Features Section */}
      <section className="py-20 bg-slate-50">
        <FeaturesAI section={{
          title: page.aiFeatures.title,
          subtitle: page.aiFeatures.subtitle,
          description: page.aiFeatures.description,
          features: aiFeatures
        }} />
      </section>

      {/* Trending Styles Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-slate-100">
        <TrendingStyles section={{
          title: page.trends.title,
          subtitle: page.trends.subtitle,
          description: page.trends.description,
          items: trendingData,
          viewAllLink: '/men'
        }} />
      </section>

      {/* Face Shape Matcher */}
      <section className="py-20 bg-white">
        <FaceShapeMatcher section={{
          title: page.faceShape.title,
          subtitle: page.faceShape.subtitle,
          description: page.faceShape.description,
          shapes: []  // TODO: 填入实际的 FaceShape 数组
        }} />
      </section>

      {/* Salon Finder Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900">
              {page.salonFinder.title}
            </h2>
            <p className="text-xl text-slate-600 mb-4">
              {page.salonFinder.subtitle}
            </p>
            <p className="text-slate-500">
              {page.salonFinder.description}
            </p>
          </div>
          
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-xl shadow-lg p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-slate-900">Find Local Salons</h3>
                  <p className="text-slate-600">Discover skilled professionals near you</p>
                </div>
              </div>
              
              <div className="flex gap-4">
                <input 
                  type="text" 
                  placeholder="Enter your city or zip code"
                  className="flex-1 px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  Search
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section - 简化版本 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <FAQ section={{
            name: 'faq',
            title: page.faq.title,
            description: page.faq.subtitle,
            items: page.faq.items  // 直接使用，因为结构已经匹配
          }} />
        </div>
      </section>

      {/* Final CTA */}
      <CTA section={{
        name: 'cta',
        title: page.cta.title,
        description: page.cta.description,
        buttons: [
          { title: page.cta.primaryButton, url: '/#aiTryOn' },
          { title: page.cta.secondaryButton, url: '/men' }
        ]
      }} />
    </main>
  );
}