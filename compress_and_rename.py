#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片压缩和重命名工具
用法: python compress_and_rename.py
"""

from PIL import Image
import os
import json
from datetime import datetime

# 配置区域 - 可以根据项目修改
CONFIG = {
    "folder_path": "C:/Users/<USER>/Desktop/pic",  # 图片文件夹路径
    "target_size_kb": 100,                     # 目标文件大小(KB)
    "quality_start": 95,                       # 起始压缩质量
    "quality_min": 10,                         # 最低压缩质量
    "output_format": "webp",                   # 输出格式
    "rename_rules": [                          # 重命名规则列表
        # 按顺序重命名，如果列表为空则使用原文件名
        "ultra-short",
        "fade-styles", 
        "specialty",
        "short-hair",
        "medium-hair",
        "korean-styles",
        "buzz-cut",
        "taper-fade",
        "crew-cut",
        "modern-mullet",
        "undercut",
        "korean-cut",
        "textured-crop",
        "mid-fade",
        "modern-buzz",
        "korean-style",
        "mens-hairstyles-hero"
    ],
    "sort_method": "time",                     # 排序方式: name, time, size
    "sort_reverse": False,                     # 是否倒序
    "prefix": "",                              # 文件名前缀
    "backup_original": False                   # 是否备份原文件
}

def log_message(message):
    """打印带时间戳的日志"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def show_current_config():
    """显示当前配置"""
    print("\n📋 当前配置:")
    print(f"  📁 文件夹: {CONFIG['folder_path']}")
    print(f"  📦 目标大小: {CONFIG['target_size_kb']}KB")
    print(f"  📐 排序方式: {CONFIG['sort_method']} ({'倒序' if CONFIG['sort_reverse'] else '正序'})")
    print(f"  📝 重命名规则数量: {len(CONFIG['rename_rules'])}")
    if CONFIG['rename_rules']:
        print(f"  📋 前3个规则: {', '.join(CONFIG['rename_rules'][:3])}...")
    if CONFIG['prefix']:
        print(f"  🏷️  文件前缀: {CONFIG['prefix']}")

def get_image_files(folder_path, sort_method="name", reverse=False):
    """获取并排序图片文件列表"""
    image_files = []
    extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.webp')
    
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        if os.path.isfile(file_path) and filename.lower().endswith(extensions):
            image_files.append(file_path)
    
    # 排序
    if sort_method == "name":
        image_files.sort(key=lambda x: os.path.basename(x), reverse=reverse)
    elif sort_method == "time":
        image_files.sort(key=lambda x: os.path.getmtime(x), reverse=reverse)
    elif sort_method == "size":
        image_files.sort(key=lambda x: os.path.getsize(x), reverse=reverse)
    
    return image_files

def compress_image(input_path, output_path, target_size, quality_start=95, quality_min=10):
    """压缩图片到指定大小"""
    try:
        with Image.open(input_path) as img:
            # 转换颜色模式
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert("RGB")
            
            quality = quality_start
            
            # 压缩循环
            while quality >= quality_min:
                img.save(output_path, "WEBP", quality=quality)
                current_size = os.path.getsize(output_path)
                
                if current_size <= target_size:
                    break
                quality -= 5
            
            log_message(f"✅ 完成: {os.path.basename(output_path)} - {current_size/1024:.1f}KB (质量: {quality}%)")
            return True
            
    except Exception as e:
        log_message(f"❌ 处理失败 {os.path.basename(input_path)}: {e}")
        return False

def generate_output_filename(input_file, index, config):
    """生成输出文件名"""
    folder_path = os.path.dirname(input_file)
    rename_rules = config.get("rename_rules", [])
    prefix = config.get("prefix", "")
    output_format = config.get("output_format", "webp")
    
    # 1. 如果有重命名规则且索引在范围内
    if rename_rules and index < len(rename_rules):
        base_name = rename_rules[index]
    else:
        # 2. 使用原文件名（去除扩展名）
        base_name = os.path.splitext(os.path.basename(input_file))[0]
    
    # 3. 添加前缀
    if prefix:
        base_name = f"{prefix}{base_name}"
    
    # 4. 生成完整路径
    output_filename = f"{base_name}.{output_format}"
    return os.path.join(folder_path, output_filename)

def interactive_setup():
    """交互式设置"""
    print("🛠️  图片压缩和重命名工具 - 交互式设置")
    print("=" * 50)
    
    # 显示当前配置
    show_current_config()
    
    # 询问是否要修改配置
    modify = input(f"\n🔧 是否要修改配置? (y/N): ").strip().lower()
    if modify != 'y':
        return CONFIG
    
    # 文件夹路径
    folder = input(f"\n📁 图片文件夹路径 (当前: {CONFIG['folder_path']}): ").strip()
    if folder:
        CONFIG['folder_path'] = folder.replace('"', '')
    
    # 排序方式
    print(f"\n📋 选择文件排序方式 (当前: {CONFIG['sort_method']}):")
    print("1. 按文件名排序")
    print("2. 按修改时间排序")
    print("3. 按文件大小排序")
    
    sort_choice = input("请选择 (1-3，回车保持不变): ").strip()
    if sort_choice == "2":
        CONFIG['sort_method'] = "time"
    elif sort_choice == "3":
        CONFIG['sort_method'] = "size"
    elif sort_choice == "1":
        CONFIG['sort_method'] = "name"
    
    # 排序顺序
    current_order = "倒序" if CONFIG['sort_reverse'] else "正序"
    order = input(f"📶 排序顺序 (当前: {current_order}) - 倒序? (y/N): ").strip().lower()
    if order == 'y':
        CONFIG['sort_reverse'] = True
    elif order == 'n':
        CONFIG['sort_reverse'] = False
    
    # 重命名选项
    print(f"\n📝 重命名选项 (当前有 {len(CONFIG['rename_rules'])} 个规则):")
    print("1. 保持当前重命名列表")
    print("2. 简单数字编号 (01, 02, 03...)")
    print("3. 保持原文件名")
    print("4. 自定义前缀")
    
    rename_choice = input("请选择 (1-4): ").strip()
    
    if rename_choice == "2":
        CONFIG['rename_rules'] = [f"{i:02d}" for i in range(1, 51)]  # 01-50
        CONFIG['prefix'] = ""
    elif rename_choice == "3":
        CONFIG['rename_rules'] = []
        CONFIG['prefix'] = ""
    elif rename_choice == "4":
        prefix = input("请输入前缀: ").strip()
        CONFIG['prefix'] = prefix
        CONFIG['rename_rules'] = []
    
    # 压缩设置
    size_input = input(f"🗜️  目标文件大小KB (当前: {CONFIG['target_size_kb']}): ").strip()
    if size_input.isdigit():
        CONFIG['target_size_kb'] = int(size_input)
    
    return CONFIG

def process_images(config):
    """处理图片"""
    folder_path = config['folder_path']
    target_size = config['target_size_kb'] * 1024
    
    if not os.path.exists(folder_path):
        log_message(f"❌ 文件夹不存在: {folder_path}")
        return False
    
    # 获取图片文件列表
    image_files = get_image_files(
        folder_path, 
        config['sort_method'], 
        config['sort_reverse']
    )
    
    if not image_files:
        log_message("❌ 没有找到图片文件!")
        return False
    
    log_message(f"🔍 找到 {len(image_files)} 个图片文件")
    log_message(f"📋 排序方式: {config['sort_method']} ({'倒序' if config['sort_reverse'] else '正序'})")
    
    # 显示文件列表
    print("\n📄 文件处理顺序:")
    for i, file_path in enumerate(image_files, 1):
        output_path = generate_output_filename(file_path, i-1, config)
        input_name = os.path.basename(file_path)
        output_name = os.path.basename(output_path)
        # 如果文件名相同，显示"保持不变"
        if input_name == output_name:
            print(f"  {i:2d}. {input_name} (保持不变)")
        else:
            print(f"  {i:2d}. {input_name} -> {output_name}")
    
    # 确认处理
    proceed = input(f"\n🚀 开始处理这 {len(image_files)} 个文件? (Y/n): ").strip().lower()
    if proceed == 'n':
        return False
    
    # 开始处理
    log_message("🚀 开始批量处理...")
    success_count = 0
    
    for i, input_file in enumerate(image_files):
        output_path = generate_output_filename(input_file, i, config)
        
        log_message(f"🔄 [{i+1}/{len(image_files)}] {os.path.basename(input_file)} -> {os.path.basename(output_path)}")
        
        if compress_image(input_file, output_path, target_size, config['quality_start'], config['quality_min']):
            success_count += 1
            
            # 删除原文件（如果输出文件名不同）
            if input_file != output_path and not config.get('backup_original', False):
                try:
                    os.remove(input_file)
                    log_message(f"🗑️  删除原文件: {os.path.basename(input_file)}")
                except:
                    log_message(f"⚠️  无法删除原文件: {os.path.basename(input_file)}")
    
    log_message(f"🎉 处理完成! 成功: {success_count}/{len(image_files)}")
    return True

def save_config(config, filename="config.json"):
    """保存配置到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        log_message(f"💾 配置已保存到: {filename}")
    except Exception as e:
        log_message(f"❌ 保存配置失败: {e}")

def load_config(filename="config.json"):
    """从文件加载配置"""
    global CONFIG
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        # 更新全局CONFIG
        CONFIG.update(loaded_config)
        log_message(f"📁 配置已加载: {filename}")
        
        # 显示加载后的配置
        show_current_config()
        return True
    except Exception as e:
        log_message(f"❌ 加载配置失败: {e}")
        return False

def quick_start():
    """快速开始模式"""
    print("⚡ 快速开始模式")
    show_current_config()
    
    proceed = input(f"\n🚀 使用当前配置直接处理? (Y/n): ").strip().lower()
    if proceed == 'n':
        return False
    
    return process_images(CONFIG)

def main():
    """主函数"""
    import sys
    
    print("🖼️  图片压缩和重命名工具")
    print("=" * 50)
    
    # 检查是否有配置文件
    config_loaded = False
    if os.path.exists("config.json"):
        use_config = input("📄 发现配置文件，是否使用? (Y/n): ").strip().lower()
        if use_config != 'n':
            config_loaded = load_config()
    
    # 如果有命令行参数，直接使用配置文件模式
    if len(sys.argv) > 1 and sys.argv[1] == "auto":
        log_message("🔧 使用配置文件模式")
        if config_loaded:
            process_images(CONFIG)
        else:
            log_message("❌ 没有找到有效的配置文件")
    else:
        # 选择模式
        if config_loaded:
            print("\n🎯 选择模式:")
            print("1. 快速开始 (使用当前配置)")
            print("2. 交互式设置")
            
            mode_choice = input("请选择 (1-2): ").strip()
            
            if mode_choice == "1":
                quick_start()
            else:
                # 交互式模式
                config = interactive_setup()
                
                # 询问是否保存配置
                save_choice = input(f"\n💾 是否保存当前配置? (y/N): ").strip().lower()
                if save_choice == 'y':
                    save_config(config)
                
                # 处理图片
                process_images(config)
        else:
            # 交互式模式
            config = interactive_setup()
            
            # 询问是否保存配置
            save_choice = input(f"\n💾 是否保存当前配置? (y/N): ").strip().lower()
            if save_choice == 'y':
                save_config(config)
            
            # 处理图片
            process_images(config)

if __name__ == "__main__":
    main() 