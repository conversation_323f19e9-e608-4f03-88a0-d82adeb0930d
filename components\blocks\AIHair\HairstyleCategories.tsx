'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface Category {
  id: string;
  name: string;
  description: string;
  image: string;
  count: number;
  url: string;
}

interface HairstyleCategoriesProps {
  section: {
    title: string;
    subtitle?: string;
    description?: string;
    categories: Category[];
  };
}

export default function HairstyleCategories({ section }: HairstyleCategoriesProps) {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  
  return (
    <section className="py-20 bg-gradient-to-b from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white">
            {section.title}
          </h2>
          
          {section.subtitle && (
            <p className="text-xl text-slate-700 dark:text-slate-300 mb-4">
              {section.subtitle}
            </p>
          )}
          
          {section.description && (
            <p className="text-slate-600 dark:text-slate-400">
              {section.description}
            </p>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {section.categories.map((category, index) => (
            <div key={category.id}>
              <Link 
                href={category.url}
                className="block"
                onMouseEnter={() => setHoveredCategory(category.id)}
                onMouseLeave={() => setHoveredCategory(null)}
              >
                <div className={`relative h-80 rounded-xl overflow-hidden transition-all duration-300 ${hoveredCategory === category.id ? 'shadow-2xl scale-105' : 'shadow-lg'}`}>
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className={`object-cover transition-transform duration-500 ${hoveredCategory === category.id ? 'scale-110' : 'scale-100'}`}
                  />
                  
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent flex flex-col justify-end p-6">
                    <h3 className="text-2xl font-bold text-white mb-2">{category.name}</h3>
                    <p className="text-slate-200 mb-4 line-clamp-2">{category.description}</p>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-slate-300">{category.count} Styles</span>
                      
                      <span className={`text-blue-300 flex items-center gap-1 transition-opacity duration-300 ${hoveredCategory === category.id ? 'opacity-100' : 'opacity-0'}`}>
                        View All
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}