# 🔧 通用关键词分类算法框架

## 📋 概述

这是一个高度可复用的关键词分类算法框架，专门用于将大量关键词自动分配到不同的页面或类别中。该框架已经在Buzzcut AI项目中成功处理了6442个关键词的分类任务，达到78%的覆盖率。

## 🎯 核心特点

- **🔧 高度可配置**: 支持精确匹配、正则表达式匹配、搜索量阈值设置
- **📊 智能评分**: 基于匹配度、优先级、搜索量的综合评分系统
- **🚀 批量处理**: 支持大规模关键词批量分类（已验证6000+词汇）
- **📈 统计分析**: 提供详细的分类统计和覆盖率分析
- **💾 多种导出**: 支持表格格式和平铺格式导出
- **🌍 多语言**: 支持中英文等多语言关键词处理

## 🚀 快速开始

```python
from utils.keyword_classifier_framework import (
    KeywordClassificationFramework,
    ClassificationRule,
    DataProcessor,
    ResultExporter,
    KeywordData
)

# 1. 创建分类器
classifier = KeywordClassificationFramework()

# 2. 添加分类规则
rule = ClassificationRule(
    page_url="/electronics/mobile/",
    description="手机专区",
    exact_keywords=["iphone", "samsung", "xiaomi"],
    regex_patterns=[r'\biphone\b', r'\bphone\b'],
    volume_threshold=100,
    max_keywords=50,
    priority=1
)
classifier.add_rule(rule)

# 3. 设置兜底规则
fallback = ClassificationRule(
    page_url="/other/",
    description="其他",
    regex_patterns=[r'.*'],
    volume_threshold=1,
    max_keywords=10000,
    priority=999
)
classifier.set_fallback_rule(fallback)

# 4. 准备数据
keywords = [
    KeywordData("iphone 15", 1000000, 8.5),
    KeywordData("samsung galaxy", 800000, 7.2)
]

# 5. 执行分类
results = classifier.classify_batch(keywords)

# 6. 导出结果
ResultExporter.export_to_csv(results, "results.csv", "table")
```

## 📁 文件结构

```
utils/
└── keyword_classifier_framework.py    # 核心框架代码
examples/
└── keyword_usage_example.py          # 使用示例
```

## 🏗️ 核心组件

### ClassificationRule - 分类规则
定义关键词如何被分配到特定页面：

```python
ClassificationRule(
    page_url="/target/page/",           # 目标页面URL
    description="页面描述",              # 页面描述
    exact_keywords=["word1", "word2"],  # 精确匹配词（100分）
    regex_patterns=[r'pattern'],        # 正则模式（30分）
    volume_threshold=100,               # 最低搜索量阈值
    max_keywords=50,                    # 最大关键词数量
    priority=1                          # 优先级（越小越高）
)
```

### 评分机制
- **精确匹配**: 100分
- **包含匹配**: 50分  
- **正则匹配**: 30分
- **优先级调整**: 分数÷优先级值

## 💡 实际应用案例

### Buzzcut AI 项目实战
```python
# 成功处理6442个关键词，78%覆盖率
buzzcut_rule = ClassificationRule(
    page_url="/men/ultra-short/buzz-cut/",
    description="Buzz Cut 主页面",
    exact_keywords=["buzz cut", "buzz cut for men"],
    regex_patterns=[r'\bbuzz\s*cut\b(?!.*fade)'],
    volume_threshold=1000,
    max_keywords=30,
    priority=2
)
```

### 电商网站分类
```python
mobile_rule = ClassificationRule(
    page_url="/electronics/mobile/",
    description="手机专区",
    exact_keywords=["iphone", "samsung", "xiaomi"],
    regex_patterns=[r'\biphone\b', r'\bphone\b'],
    volume_threshold=100,
    max_keywords=100,
    priority=1
)
```

## 📊 导出格式

### 表格格式
```csv
主关键词,搜索量,KD值,页面,话题,大纲
=== 首页 (/) ===,共5个词,总量25M,,,
buzz cut,5M,0,/,首页,
```

### 平铺格式
```csv
keyword,search_volume,competition,target_page,page_description
buzz cut,5000000,0,/,首页
```

## 🔧 高级功能

### 统计分析
```python
stats = classifier.get_statistics()
print(f"覆盖率: {stats['coverage_rate']:.1%}")
```

### 从CSV加载数据
```python
keywords_data = DataProcessor.load_keywords_from_csv("keywords.csv")
```

### 多种导出选项
```python
# 表格格式
ResultExporter.export_to_csv(results, "table.csv", "table")
# 平铺格式  
ResultExporter.export_to_csv(results, "flat.csv", "flat")
```

## 📈 性能表现

- ✅ **已验证**: 成功处理6442个关键词
- ✅ **覆盖率**: 78%分类成功率
- ✅ **处理速度**: 每1000个关键词显示进度
- ✅ **内存优化**: 支持大规模数据批量处理

## 🛠️ 自定义扩展

可以继承基类来实现自定义逻辑：

```python
class CustomClassificationRule(ClassificationRule):
    def calculate_match_score(self, keyword: str, search_volume: int) -> float:
        base_score = super().calculate_match_score(keyword, search_volume)
        # 添加自定义评分逻辑
        if "premium" in keyword.lower():
            base_score *= 1.5
        return min(base_score, 100.0)
```

## 📝 使用场景

- **SEO优化**: 关键词页面分配和内容规划
- **电商分类**: 产品关键词自动分类
- **内容管理**: 文章关键词标签分配
- **广告投放**: 关键词分组和预算分配
- **竞争分析**: 关键词竞争度分析

## 🤝 贡献

欢迎提交Issue和Pull Request来改进框架！

## 📄 许可证

MIT License - 可自由用于商业和非商业项目 